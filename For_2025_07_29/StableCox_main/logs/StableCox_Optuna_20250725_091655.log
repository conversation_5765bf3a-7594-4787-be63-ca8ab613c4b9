2025-07-25 09:16:55 [INFO] StableCox Optuna Optimization Started
2025-07-25 09:16:55 [INFO] Log file: logs\StableCox_Optuna_20250725_091655.log
2025-07-25 09:16:55 [INFO] MPI Configuration: 10 processes
2025-07-25 09:16:55 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'none', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'LogLogistic', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 10, 'result_dir': 'results', 'n_trials': 50, 'test_size': 0.1}
2025-07-25 09:16:55 [INFO] Model using in this file is: LogLogistic
2025-07-25 09:16:55 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 09:16:55 [INFO] Data scaler splite with random state 9
2025-07-25 09:16:55 [INFO] Starting Round 1/10
2025-07-25 09:16:55 [INFO] --- Starting Round 0 ---
2025-07-25 09:16:55 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 09:16:55 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 09:16:55 [INFO] Use 50 Optuna trials
2025-07-25 09:18:19 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.8776229616539883
      x: [ 8.487e-02  1.783e-01 ... -3.708e-01  1.418e+00]
    nit: 200
    jac: [-1.842e-03  2.302e-03 ... -4.207e-04  5.998e-04]
   nfev: 207
   njev: 200

2025-07-25 09:18:39 [WARNING] Fold 2 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9366849270016906
      x: [ 4.166e-01 -1.157e+00 ... -2.536e-01  1.169e+00]
    nit: 200
    jac: [-1.178e-04  2.320e-04 ... -6.003e-04  1.253e-03]
   nfev: 209
   njev: 200

2025-07-25 09:23:37 [WARNING] Fold 1 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9446902995523372
      x: [ 7.681e-01 -5.761e-01 ... -3.718e-01  1.139e+00]
    nit: 200
    jac: [ 2.205e-04 -2.340e-05 ... -7.254e-05 -9.994e-05]
   nfev: 204
   njev: 200

2025-07-25 09:25:00 [WARNING] Fold 8 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9885534549418358
      x: [ 2.796e-01 -9.465e-01 ... -1.970e-02  1.021e+00]
    nit: 200
    jac: [-3.891e-05  7.479e-05 ...  4.726e-06  3.174e-04]
   nfev: 205
   njev: 200

2025-07-25 09:28:45 [INFO] Hyperparameter optimization completed! Best validation C-index: 0.5582
2025-07-25 09:28:45 [INFO] Best parameters: {'penalizer': 0.25880900508903776, 'penalizer2': 0.024537341608226893, 'topN': 5}
2025-07-25 09:28:45 [INFO] === Stage 2: Final model training and evaluation ===
2025-07-25 09:28:45 [INFO] Using optimized weight clipping: min=0.004483, max=2.60
2025-07-25 09:28:45 [INFO] Final model selected 4 features: ['e', '_', 'h', 'U']
