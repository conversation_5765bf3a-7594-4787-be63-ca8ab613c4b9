2025-07-25 09:34:39 [INFO] StableCox Optuna Optimization Started
2025-07-25 09:34:39 [INFO] Log file: logs\StableCox_Optuna_20250725_093439.log
2025-07-25 09:34:39 [INFO] MPI Configuration: 10 processes
2025-07-25 09:34:39 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'none', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'LogLogistic', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 10, 'result_dir': 'results', 'n_trials': 50, 'test_size': 0.1}
2025-07-25 09:34:39 [INFO] Model using in this file is: LogLogistic
2025-07-25 09:34:39 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 09:34:39 [INFO] Data scaler splite with random state 9
2025-07-25 09:34:39 [INFO] Starting Round 1/10
2025-07-25 09:34:39 [INFO] --- Starting Round 0 ---
2025-07-25 09:34:39 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 09:34:39 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 09:34:39 [INFO] Use 50 Optuna trials
