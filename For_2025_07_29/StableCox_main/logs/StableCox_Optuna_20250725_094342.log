2025-07-25 09:43:42 [INFO] StableCox Optuna Optimization Started
2025-07-25 09:43:42 [INFO] Log file: logs\StableCox_Optuna_20250725_094342.log
2025-07-25 09:43:42 [INFO] MPI Configuration: 1 processes
2025-07-25 09:43:42 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'SRDO', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'LogLogistic', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 1, 'result_dir': 'results', 'n_trials': 50, 'use_fixed_split': True, 'increase_test_size': False}
2025-07-25 09:43:42 [INFO] Model using in this file is: LogLogistic
2025-07-25 09:43:42 [INFO] Starting Round 1/1
2025-07-25 09:43:42 [INFO] --- Starting Round 0 with Seed 9 ---
2025-07-25 09:43:42 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 09:43:42 [INFO] Using FIXED data split with seed 9, test_size=0.1
2025-07-25 09:43:42 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 09:43:42 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 09:43:42 [INFO] Use 50 Optuna trials
2025-07-25 09:43:43 [WARNING] Fold 0 failed: "['Intercept'] not in index"
2025-07-25 09:43:43 [WARNING] Fold 1 failed: "['Intercept'] not in index"
2025-07-25 09:43:44 [WARNING] Fold 2 failed: "['Intercept'] not in index"
2025-07-25 09:43:45 [WARNING] Fold 3 failed: "['Intercept'] not in index"
2025-07-25 09:43:46 [WARNING] Fold 4 failed: "['Intercept'] not in index"
2025-07-25 09:43:46 [WARNING] Fold 0 failed: "['Intercept'] not in index"
2025-07-25 09:43:47 [WARNING] Fold 1 failed: "['Intercept'] not in index"
2025-07-25 09:43:48 [WARNING] Fold 2 failed: "['Intercept'] not in index"
2025-07-25 09:43:49 [WARNING] Fold 3 failed: "['Intercept'] not in index"
