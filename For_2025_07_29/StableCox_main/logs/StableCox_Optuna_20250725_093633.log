2025-07-25 09:36:33 [INFO] StableCox Optuna Optimization Started
2025-07-25 09:36:33 [INFO] Log file: logs\StableCox_Optuna_20250725_093633.log
2025-07-25 09:36:33 [INFO] MPI Configuration: 1 processes
2025-07-25 09:36:33 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'SRDO', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'Weighted_cox', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 1, 'result_dir': 'results', 'n_trials': 50, 'use_fixed_split': True, 'increase_test_size': False}
2025-07-25 09:36:33 [INFO] Model using in this file is: Weighted_cox
2025-07-25 09:36:33 [INFO] Starting Round 1/1
2025-07-25 09:36:33 [INFO] --- Starting Round 0 with Seed 9 ---
2025-07-25 09:36:33 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 09:36:33 [INFO] Using FIXED data split with seed 9, test_size=0.1
2025-07-25 09:36:33 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 09:36:33 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 09:36:33 [INFO] Use 50 Optuna trials
