2025-07-25 09:49:30 [INFO] StableCox Optuna Optimization Started
2025-07-25 09:49:30 [INFO] Log file: logs\StableCox_Optuna_20250725_094930.log
2025-07-25 09:49:30 [INFO] MPI Configuration: 1 processes
2025-07-25 09:49:30 [INFO] Parameters: {'PATH': 'processed_equipment_data_new.csv', 'seed': 9, 'n_splits': 10, 'reweighting': 'none', 'decorrelation_type': 'global', 'iters_balance': 2500, 'backend': 'LogLogistic', 'paradigm': 'regr', 'penalizer': 0.03, 'penalizer2': 0.03, 'topN': 5, 'times': 1, 'result_dir': 'results', 'n_trials': 50, 'test_size': 0.1}
2025-07-25 09:49:30 [INFO] Model using in this file is: LogLogistic
2025-07-25 09:49:30 [INFO] Successfully loaded data from processed_equipment_data_new.csv. Shape: (598, 187)
2025-07-25 09:49:30 [INFO] Data scaler splite with random state 9
2025-07-25 09:49:30 [INFO] Starting Round 1/1
2025-07-25 09:49:30 [INFO] --- Starting Round 0 ---
2025-07-25 09:49:30 [INFO] Data split - Train/Val: (538, 182), Test: (60, 182)
2025-07-25 09:49:30 [INFO] === Stage 1: Starting hyperparameter optimization ===
2025-07-25 09:49:30 [INFO] Use 50 Optuna trials
2025-07-25 09:49:34 [WARNING] Fold 5 failed: 
               Fitting did not converge. Try the following:

               0. Are there any lifelines warnings outputted during the `fit`?
               1. Inspect your DataFrame: does everything look as expected?
               2. Try scaling your duration vector down, i.e. `df[duration_col] = df[duration_col]/100`
               3. Is there high-collinearity in the dataset? Try using the variance inflation factor (VIF) to find redundant variables.
               4. Try using an alternate minimizer: ``fitter._scipy_fit_method = "SLSQP"``.
               5. Trying adding a small penalizer (or changing it, if already present). Example: `LogLogisticAFTFitter(penalizer=0.01).fit(...)`.
               6. Are there any extreme outliers? Try modeling them or dropping them to see if it helps convergence.

               minimum_results= message: Iteration limit reached
success: False
 status: 9
    fun: 1.9522380344191446
      x: [-1.109e-01 -7.891e-01 ... -3.228e-02  1.095e+00]
    nit: 200
    jac: [-4.437e-06  2.318e-06 ...  1.102e-04 -4.431e-05]
   nfev: 208
   njev: 200

