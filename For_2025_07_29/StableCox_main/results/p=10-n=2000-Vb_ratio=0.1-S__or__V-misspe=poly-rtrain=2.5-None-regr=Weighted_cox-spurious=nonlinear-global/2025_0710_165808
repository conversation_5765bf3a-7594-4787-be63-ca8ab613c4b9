2025-07-10 16:58:08,887 - INFO: p: 10
2025-07-10 16:58:08,889 - INFO: n: 2000
2025-07-10 16:58:08,890 - INFO: V_ratio: 0.5
2025-07-10 16:58:08,891 - INFO: Vb_ratio: 0.1
2025-07-10 16:58:08,891 - INFO: true_func: linear
2025-07-10 16:58:08,892 - INFO: mode: S_|_V
2025-07-10 16:58:08,892 - INFO: misspe: poly
2025-07-10 16:58:08,894 - INFO: corr_s: 0.9
2025-07-10 16:58:08,895 - INFO: corr_v: 0.1
2025-07-10 16:58:08,895 - INFO: mms_strength: 1.0
2025-07-10 16:58:08,895 - INFO: spurious: nonlinear
2025-07-10 16:58:08,896 - INFO: r_train: 2.5
2025-07-10 16:58:08,897 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-10 16:58:08,897 - INFO: noise_variance: 0.3
2025-07-10 16:58:08,897 - INFO: reweighting: None
2025-07-10 16:58:08,898 - INFO: decorrelation_type: global
2025-07-10 16:58:08,898 - INFO: order: 1
2025-07-10 16:58:08,899 - INFO: iters_balance: 2500
2025-07-10 16:58:08,899 - INFO: topN: 4
2025-07-10 16:58:08,900 - INFO: backend: Weighted_cox
2025-07-10 16:58:08,901 - INFO: paradigm: regr
2025-07-10 16:58:08,901 - INFO: iters_train: 5000
2025-07-10 16:58:08,901 - INFO: lam_backend: 0.21577931514746537
2025-07-10 16:58:08,902 - INFO: lam_backend2: 2.7712399316485526e-05
2025-07-10 16:58:08,903 - INFO: fs_type: STG
2025-07-10 16:58:08,903 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-10 16:58:08,903 - INFO: mask_threshold: 0.2
2025-07-10 16:58:08,905 - INFO: lam_STG: 3
2025-07-10 16:58:08,906 - INFO: sigma_STG: 0.1
2025-07-10 16:58:08,906 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-10 16:58:08,907 - INFO: bv_analysis: False
2025-07-10 16:58:08,908 - INFO: seed: 2
2025-07-10 16:58:08,908 - INFO: times: 10
2025-07-10 16:58:08,908 - INFO: result_dir: results
2025-07-10 16:58:08,909 - INFO: n_splits: 5
2025-07-10 16:58:08,909 - INFO: 
2025-07-10 16:58:08,909 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:08,910 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-10 16:58:08,963 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:08,965 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:08,967 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:08,967 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:08,970 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:08,981 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:08,982 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:08,983 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:08,983 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:08,984 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:08,984 - INFO: Starting regression paradigm
2025-07-10 16:58:08,984 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:08,986 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:09,084 - INFO: Fold 1 Training C-index: 0.6393
2025-07-10 16:58:09,096 - INFO: Fold 1 Test C-index: 0.5015
2025-07-10 16:58:09,097 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:09,105 - INFO: Fold 1 Validation C-index: 0.4149
2025-07-10 16:58:09,105 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:09,115 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:09,117 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:09,117 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:09,117 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,117 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,117 - INFO: Starting regression paradigm
2025-07-10 16:58:09,117 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:09,118 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:09,220 - INFO: Fold 2 Training C-index: 0.6665
2025-07-10 16:58:09,233 - INFO: Fold 2 Test C-index: 0.4653
2025-07-10 16:58:09,234 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:09,245 - INFO: Fold 2 Validation C-index: 0.4043
2025-07-10 16:58:09,246 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:09,255 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:09,258 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:09,259 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:09,259 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,261 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,261 - INFO: Starting regression paradigm
2025-07-10 16:58:09,261 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:09,262 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:09,359 - INFO: Fold 3 Training C-index: 0.6501
2025-07-10 16:58:09,369 - INFO: Fold 3 Test C-index: 0.5950
2025-07-10 16:58:09,370 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:09,378 - INFO: Fold 3 Validation C-index: 0.6170
2025-07-10 16:58:09,379 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:09,388 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:09,391 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:09,392 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:09,394 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,394 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,395 - INFO: Starting regression paradigm
2025-07-10 16:58:09,395 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:09,395 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:09,518 - INFO: Fold 4 Training C-index: 0.6697
2025-07-10 16:58:09,528 - INFO: Fold 4 Test C-index: 0.4815
2025-07-10 16:58:09,529 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:09,537 - INFO: Fold 4 Validation C-index: 0.4468
2025-07-10 16:58:09,537 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:09,547 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:09,549 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:09,550 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:09,552 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,552 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,556 - INFO: Starting regression paradigm
2025-07-10 16:58:09,558 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:09,558 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:09,677 - INFO: Fold 5 Training C-index: 0.6836
2025-07-10 16:58:09,692 - INFO: Fold 5 Test C-index: 0.4336
2025-07-10 16:58:09,693 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:09,707 - INFO: Fold 5 Validation C-index: 0.3936
2025-07-10 16:58:09,708 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:09,708 - INFO: C-indices per fold: ['0.4149', '0.4043', '0.6170', '0.4468', '0.3936']
2025-07-10 16:58:09,710 - INFO: Mean C-index: 0.4553
2025-07-10 16:58:09,710 - INFO: Std Dev of C-index: 0.0828
2025-07-10 16:58:09,711 - INFO: Worst C-index: 0.3936
2025-07-10 16:58:09,712 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:09,712 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-10 16:58:09,735 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:09,737 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:09,738 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:09,739 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:09,745 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:09,757 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:09,760 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:09,760 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:09,761 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,761 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,762 - INFO: Starting regression paradigm
2025-07-10 16:58:09,762 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:09,763 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:09,861 - INFO: Fold 1 Training C-index: 0.6822
2025-07-10 16:58:09,873 - INFO: Fold 1 Test C-index: 0.4754
2025-07-10 16:58:09,875 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:09,887 - INFO: Fold 1 Validation C-index: 0.6277
2025-07-10 16:58:09,888 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:09,909 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:09,911 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:09,912 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:09,913 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,913 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:09,914 - INFO: Starting regression paradigm
2025-07-10 16:58:09,915 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:09,915 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:10,012 - INFO: Fold 2 Training C-index: 0.6430
2025-07-10 16:58:10,025 - INFO: Fold 2 Test C-index: 0.6160
2025-07-10 16:58:10,026 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:10,035 - INFO: Fold 2 Validation C-index: 0.5851
2025-07-10 16:58:10,037 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:10,048 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:10,051 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:10,052 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:10,054 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:10,056 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:10,058 - INFO: Starting regression paradigm
2025-07-10 16:58:10,059 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:10,059 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:10,181 - INFO: Fold 3 Training C-index: 0.6373
2025-07-10 16:58:10,230 - INFO: Fold 3 Test C-index: 0.4776
2025-07-10 16:58:10,232 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:10,277 - INFO: Fold 3 Validation C-index: 0.5106
2025-07-10 16:58:10,278 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:10,340 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:10,348 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:10,349 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:10,350 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:10,351 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:10,351 - INFO: Starting regression paradigm
2025-07-10 16:58:10,352 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:10,352 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:10,850 - INFO: Fold 4 Training C-index: 0.6696
2025-07-10 16:58:10,900 - INFO: Fold 4 Test C-index: 0.5175
2025-07-10 16:58:10,902 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:10,947 - INFO: Fold 4 Validation C-index: 0.5638
2025-07-10 16:58:10,948 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:11,003 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:11,011 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:11,012 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:11,012 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:11,013 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:11,014 - INFO: Starting regression paradigm
2025-07-10 16:58:11,014 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:11,015 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:11,504 - INFO: Fold 5 Training C-index: 0.7015
2025-07-10 16:58:11,551 - INFO: Fold 5 Test C-index: 0.4500
2025-07-10 16:58:11,552 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:11,600 - INFO: Fold 5 Validation C-index: 0.5106
2025-07-10 16:58:11,602 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:11,602 - INFO: C-indices per fold: ['0.6277', '0.5851', '0.5106', '0.5638', '0.5106']
2025-07-10 16:58:11,603 - INFO: Mean C-index: 0.5596
2025-07-10 16:58:11,603 - INFO: Std Dev of C-index: 0.0449
2025-07-10 16:58:11,604 - INFO: Worst C-index: 0.5106
2025-07-10 16:58:11,606 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:11,606 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-10 16:58:11,676 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:11,680 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:11,683 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:11,684 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:11,693 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:11,743 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:11,748 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:11,748 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:11,748 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:11,749 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:11,749 - INFO: Starting regression paradigm
2025-07-10 16:58:11,750 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:11,750 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:12,295 - INFO: Fold 1 Training C-index: 0.7004
2025-07-10 16:58:12,344 - INFO: Fold 1 Test C-index: 0.4894
2025-07-10 16:58:12,346 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:12,392 - INFO: Fold 1 Validation C-index: 0.4505
2025-07-10 16:58:12,393 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:12,453 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:12,461 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:12,462 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:12,463 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:12,464 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:12,465 - INFO: Starting regression paradigm
2025-07-10 16:58:12,465 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:12,465 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:13,117 - INFO: Fold 2 Training C-index: 0.6944
2025-07-10 16:58:13,141 - INFO: Fold 2 Test C-index: 0.4654
2025-07-10 16:58:13,142 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:13,165 - INFO: Fold 2 Validation C-index: 0.3077
2025-07-10 16:58:13,166 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:13,207 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:13,221 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:13,221 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:13,222 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:13,223 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:13,223 - INFO: Starting regression paradigm
2025-07-10 16:58:13,224 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:13,224 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:13,512 - INFO: Fold 3 Training C-index: 0.6729
2025-07-10 16:58:13,533 - INFO: Fold 3 Test C-index: 0.5900
2025-07-10 16:58:13,533 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:13,553 - INFO: Fold 3 Validation C-index: 0.4835
2025-07-10 16:58:13,553 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:13,578 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:13,582 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:13,583 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:13,584 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:13,584 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:13,584 - INFO: Starting regression paradigm
2025-07-10 16:58:13,585 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:13,585 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:13,794 - INFO: Fold 4 Training C-index: 0.6640
2025-07-10 16:58:13,815 - INFO: Fold 4 Test C-index: 0.5866
2025-07-10 16:58:13,815 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:13,833 - INFO: Fold 4 Validation C-index: 0.4725
2025-07-10 16:58:13,834 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:13,856 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:13,859 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:13,860 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:13,860 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:13,860 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:13,861 - INFO: Starting regression paradigm
2025-07-10 16:58:13,861 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:13,861 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:14,073 - INFO: Fold 5 Training C-index: 0.6774
2025-07-10 16:58:14,113 - INFO: Fold 5 Test C-index: 0.5579
2025-07-10 16:58:14,113 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:14,152 - INFO: Fold 5 Validation C-index: 0.4945
2025-07-10 16:58:14,153 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:14,154 - INFO: C-indices per fold: ['0.4505', '0.3077', '0.4835', '0.4725', '0.4945']
2025-07-10 16:58:14,154 - INFO: Mean C-index: 0.4418
2025-07-10 16:58:14,155 - INFO: Std Dev of C-index: 0.0686
2025-07-10 16:58:14,156 - INFO: Worst C-index: 0.3077
2025-07-10 16:58:14,157 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:14,158 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-10 16:58:14,223 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:14,227 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:14,230 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:14,230 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:14,241 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:14,293 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:14,297 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:14,298 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:14,299 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:14,300 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:14,300 - INFO: Starting regression paradigm
2025-07-10 16:58:14,301 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:14,301 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:14,624 - INFO: Fold 1 Training C-index: 0.6759
2025-07-10 16:58:14,649 - INFO: Fold 1 Test C-index: 0.3995
2025-07-10 16:58:14,650 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:14,679 - INFO: Fold 1 Validation C-index: 0.5620
2025-07-10 16:58:14,679 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:14,710 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:14,715 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:14,715 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:14,716 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:14,716 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:14,716 - INFO: Starting regression paradigm
2025-07-10 16:58:14,716 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:14,717 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:15,026 - INFO: Fold 2 Training C-index: 0.6438
2025-07-10 16:58:15,051 - INFO: Fold 2 Test C-index: 0.5768
2025-07-10 16:58:15,051 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:15,072 - INFO: Fold 2 Validation C-index: 0.6446
2025-07-10 16:58:15,072 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:15,171 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:15,179 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:15,179 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:15,181 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:15,181 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:15,182 - INFO: Starting regression paradigm
2025-07-10 16:58:15,183 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:15,183 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:15,526 - INFO: Fold 3 Training C-index: 0.6468
2025-07-10 16:58:15,554 - INFO: Fold 3 Test C-index: 0.5735
2025-07-10 16:58:15,555 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:15,579 - INFO: Fold 3 Validation C-index: 0.5620
2025-07-10 16:58:15,580 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:15,606 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:15,610 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:15,610 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:15,612 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:15,612 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:15,612 - INFO: Starting regression paradigm
2025-07-10 16:58:15,613 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:15,613 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:15,843 - INFO: Fold 4 Training C-index: 0.6984
2025-07-10 16:58:15,862 - INFO: Fold 4 Test C-index: 0.4727
2025-07-10 16:58:15,862 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:15,882 - INFO: Fold 4 Validation C-index: 0.6364
2025-07-10 16:58:15,882 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:15,904 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:15,908 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:15,908 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:15,908 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:15,909 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:15,909 - INFO: Starting regression paradigm
2025-07-10 16:58:15,909 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:15,910 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:16,109 - INFO: Fold 5 Training C-index: 0.6743
2025-07-10 16:58:16,128 - INFO: Fold 5 Test C-index: 0.4309
2025-07-10 16:58:16,128 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:16,148 - INFO: Fold 5 Validation C-index: 0.4959
2025-07-10 16:58:16,148 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:16,149 - INFO: C-indices per fold: ['0.5620', '0.6446', '0.5620', '0.6364', '0.4959']
2025-07-10 16:58:16,149 - INFO: Mean C-index: 0.5802
2025-07-10 16:58:16,149 - INFO: Std Dev of C-index: 0.0549
2025-07-10 16:58:16,150 - INFO: Worst C-index: 0.4959
2025-07-10 16:58:16,151 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:16,151 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-10 16:58:16,183 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:16,185 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:16,187 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:16,187 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:16,192 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:16,236 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:16,243 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:16,244 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:16,244 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:16,245 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:16,246 - INFO: Starting regression paradigm
2025-07-10 16:58:16,246 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:16,247 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:16,678 - INFO: Fold 1 Training C-index: 0.6438
2025-07-10 16:58:16,720 - INFO: Fold 1 Test C-index: 0.5773
2025-07-10 16:58:16,720 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:16,765 - INFO: Fold 1 Validation C-index: 0.4896
2025-07-10 16:58:16,765 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:16,815 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:16,821 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:16,822 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:16,822 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:16,823 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:16,824 - INFO: Starting regression paradigm
2025-07-10 16:58:16,824 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:16,824 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:17,203 - INFO: Fold 2 Training C-index: 0.6623
2025-07-10 16:58:17,250 - INFO: Fold 2 Test C-index: 0.5475
2025-07-10 16:58:17,250 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:17,293 - INFO: Fold 2 Validation C-index: 0.4583
2025-07-10 16:58:17,294 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:17,346 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:17,353 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:17,354 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:17,355 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:17,356 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:17,356 - INFO: Starting regression paradigm
2025-07-10 16:58:17,357 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:17,358 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:17,908 - INFO: Fold 3 Training C-index: 0.6740
2025-07-10 16:58:17,951 - INFO: Fold 3 Test C-index: 0.4630
2025-07-10 16:58:17,953 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:17,996 - INFO: Fold 3 Validation C-index: 0.4583
2025-07-10 16:58:17,997 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:18,054 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:18,061 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:18,063 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:18,064 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:18,064 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:18,065 - INFO: Starting regression paradigm
2025-07-10 16:58:18,065 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:18,065 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:18,568 - INFO: Fold 4 Training C-index: 0.6614
2025-07-10 16:58:18,645 - INFO: Fold 4 Test C-index: 0.5523
2025-07-10 16:58:18,646 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:18,709 - INFO: Fold 4 Validation C-index: 0.5208
2025-07-10 16:58:18,712 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:18,772 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:18,779 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:18,779 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:18,780 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:18,781 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:18,782 - INFO: Starting regression paradigm
2025-07-10 16:58:18,782 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:18,782 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:19,248 - INFO: Fold 5 Training C-index: 0.6607
2025-07-10 16:58:19,299 - INFO: Fold 5 Test C-index: 0.4111
2025-07-10 16:58:19,300 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:19,343 - INFO: Fold 5 Validation C-index: 0.5104
2025-07-10 16:58:19,345 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:19,345 - INFO: C-indices per fold: ['0.4896', '0.4583', '0.4583', '0.5208', '0.5104']
2025-07-10 16:58:19,346 - INFO: Mean C-index: 0.4875
2025-07-10 16:58:19,346 - INFO: Std Dev of C-index: 0.0259
2025-07-10 16:58:19,347 - INFO: Worst C-index: 0.4583
2025-07-10 16:58:19,349 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:19,349 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-10 16:58:19,421 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:19,425 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:19,429 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:19,430 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:19,441 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:19,480 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:19,484 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:19,485 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:19,485 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:19,486 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:19,486 - INFO: Starting regression paradigm
2025-07-10 16:58:19,486 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:19,487 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:19,750 - INFO: Fold 1 Training C-index: 0.6455
2025-07-10 16:58:19,774 - INFO: Fold 1 Test C-index: 0.5337
2025-07-10 16:58:19,775 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:19,807 - INFO: Fold 1 Validation C-index: 0.4231
2025-07-10 16:58:19,808 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:19,857 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:19,862 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:19,862 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:19,863 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:19,863 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:19,863 - INFO: Starting regression paradigm
2025-07-10 16:58:19,864 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:19,864 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:20,319 - INFO: Fold 2 Training C-index: 0.6494
2025-07-10 16:58:20,357 - INFO: Fold 2 Test C-index: 0.5908
2025-07-10 16:58:20,358 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:20,383 - INFO: Fold 2 Validation C-index: 0.5000
2025-07-10 16:58:20,383 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:20,413 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:20,417 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:20,417 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:20,418 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:20,418 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:20,419 - INFO: Starting regression paradigm
2025-07-10 16:58:20,419 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:20,419 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:20,673 - INFO: Fold 3 Training C-index: 0.6750
2025-07-10 16:58:20,694 - INFO: Fold 3 Test C-index: 0.4825
2025-07-10 16:58:20,694 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:20,714 - INFO: Fold 3 Validation C-index: 0.3942
2025-07-10 16:58:20,714 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:20,736 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:20,739 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:20,740 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:20,740 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:20,741 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:20,741 - INFO: Starting regression paradigm
2025-07-10 16:58:20,741 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:20,741 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:20,957 - INFO: Fold 4 Training C-index: 0.6485
2025-07-10 16:58:20,976 - INFO: Fold 4 Test C-index: 0.5538
2025-07-10 16:58:20,976 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:20,995 - INFO: Fold 4 Validation C-index: 0.4519
2025-07-10 16:58:20,995 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:21,017 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:21,020 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:21,021 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:21,021 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:21,021 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:21,021 - INFO: Starting regression paradigm
2025-07-10 16:58:21,021 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:21,022 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:21,225 - INFO: Fold 5 Training C-index: 0.6420
2025-07-10 16:58:21,246 - INFO: Fold 5 Test C-index: 0.5373
2025-07-10 16:58:21,248 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:21,269 - INFO: Fold 5 Validation C-index: 0.5192
2025-07-10 16:58:21,270 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:21,270 - INFO: C-indices per fold: ['0.4231', '0.5000', '0.3942', '0.4519', '0.5192']
2025-07-10 16:58:21,270 - INFO: Mean C-index: 0.4577
2025-07-10 16:58:21,270 - INFO: Std Dev of C-index: 0.0466
2025-07-10 16:58:21,270 - INFO: Worst C-index: 0.3942
2025-07-10 16:58:21,271 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:21,272 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-10 16:58:21,329 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:21,333 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:21,337 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:21,337 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:21,347 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:21,399 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:21,405 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:21,406 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:21,407 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:21,408 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:21,408 - INFO: Starting regression paradigm
2025-07-10 16:58:21,408 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:21,409 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:21,768 - INFO: Fold 1 Training C-index: 0.6569
2025-07-10 16:58:21,805 - INFO: Fold 1 Test C-index: 0.5630
2025-07-10 16:58:21,806 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:21,841 - INFO: Fold 1 Validation C-index: 0.4444
2025-07-10 16:58:21,842 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:21,893 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:21,899 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:21,900 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:21,901 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:21,902 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:21,903 - INFO: Starting regression paradigm
2025-07-10 16:58:21,903 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:21,904 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:22,283 - INFO: Fold 2 Training C-index: 0.6821
2025-07-10 16:58:22,326 - INFO: Fold 2 Test C-index: 0.4939
2025-07-10 16:58:22,327 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:22,369 - INFO: Fold 2 Validation C-index: 0.5253
2025-07-10 16:58:22,370 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:22,429 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:22,436 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:22,437 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:22,438 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:22,439 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:22,439 - INFO: Starting regression paradigm
2025-07-10 16:58:22,440 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:22,440 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:22,935 - INFO: Fold 3 Training C-index: 0.6639
2025-07-10 16:58:22,977 - INFO: Fold 3 Test C-index: 0.5153
2025-07-10 16:58:22,978 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:23,017 - INFO: Fold 3 Validation C-index: 0.5152
2025-07-10 16:58:23,018 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:23,068 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:23,076 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:23,076 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:23,077 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:23,078 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:23,079 - INFO: Starting regression paradigm
2025-07-10 16:58:23,079 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:23,080 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:23,617 - INFO: Fold 4 Training C-index: 0.6777
2025-07-10 16:58:23,653 - INFO: Fold 4 Test C-index: 0.5151
2025-07-10 16:58:23,653 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:23,675 - INFO: Fold 4 Validation C-index: 0.5051
2025-07-10 16:58:23,676 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:23,702 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:23,705 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:23,706 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:23,706 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:23,707 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:23,707 - INFO: Starting regression paradigm
2025-07-10 16:58:23,707 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:23,707 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:24,008 - INFO: Fold 5 Training C-index: 0.6867
2025-07-10 16:58:24,051 - INFO: Fold 5 Test C-index: 0.5994
2025-07-10 16:58:24,051 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:24,095 - INFO: Fold 5 Validation C-index: 0.5758
2025-07-10 16:58:24,096 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:24,098 - INFO: C-indices per fold: ['0.4444', '0.5253', '0.5152', '0.5051', '0.5758']
2025-07-10 16:58:24,099 - INFO: Mean C-index: 0.5131
2025-07-10 16:58:24,099 - INFO: Std Dev of C-index: 0.0421
2025-07-10 16:58:24,100 - INFO: Worst C-index: 0.4444
2025-07-10 16:58:24,102 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:24,102 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-10 16:58:24,174 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:24,177 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:24,181 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:24,181 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:24,191 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:24,239 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:24,246 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:24,247 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:24,248 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:24,249 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:24,250 - INFO: Starting regression paradigm
2025-07-10 16:58:24,251 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:24,251 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:24,816 - INFO: Fold 1 Training C-index: 0.6816
2025-07-10 16:58:24,865 - INFO: Fold 1 Test C-index: 0.5442
2025-07-10 16:58:24,865 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:24,911 - INFO: Fold 1 Validation C-index: 0.5714
2025-07-10 16:58:24,911 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:24,961 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:24,968 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:24,969 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:24,970 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:24,971 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:24,971 - INFO: Starting regression paradigm
2025-07-10 16:58:24,972 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:24,973 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:25,170 - INFO: Fold 2 Training C-index: 0.6530
2025-07-10 16:58:25,183 - INFO: Fold 2 Test C-index: 0.4972
2025-07-10 16:58:25,183 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:25,194 - INFO: Fold 2 Validation C-index: 0.6735
2025-07-10 16:58:25,194 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:25,208 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:25,209 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:25,209 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:25,210 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,210 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,210 - INFO: Starting regression paradigm
2025-07-10 16:58:25,210 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:25,210 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:25,337 - INFO: Fold 3 Training C-index: 0.6722
2025-07-10 16:58:25,349 - INFO: Fold 3 Test C-index: 0.4771
2025-07-10 16:58:25,349 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:25,361 - INFO: Fold 3 Validation C-index: 0.4490
2025-07-10 16:58:25,361 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:25,374 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:25,375 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:25,375 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:25,376 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,376 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,376 - INFO: Starting regression paradigm
2025-07-10 16:58:25,376 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:25,376 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:25,501 - INFO: Fold 4 Training C-index: 0.6648
2025-07-10 16:58:25,515 - INFO: Fold 4 Test C-index: 0.5304
2025-07-10 16:58:25,515 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:25,526 - INFO: Fold 4 Validation C-index: 0.5510
2025-07-10 16:58:25,526 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:25,538 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:25,540 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:25,541 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:25,541 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,541 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,541 - INFO: Starting regression paradigm
2025-07-10 16:58:25,541 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:25,541 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:25,667 - INFO: Fold 5 Training C-index: 0.6774
2025-07-10 16:58:25,679 - INFO: Fold 5 Test C-index: 0.4526
2025-07-10 16:58:25,679 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:25,689 - INFO: Fold 5 Validation C-index: 0.3673
2025-07-10 16:58:25,689 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:25,690 - INFO: C-indices per fold: ['0.5714', '0.6735', '0.4490', '0.5510', '0.3673']
2025-07-10 16:58:25,690 - INFO: Mean C-index: 0.5224
2025-07-10 16:58:25,690 - INFO: Std Dev of C-index: 0.1053
2025-07-10 16:58:25,690 - INFO: Worst C-index: 0.3673
2025-07-10 16:58:25,691 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:25,691 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-10 16:58:25,710 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:25,712 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:25,713 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:25,713 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:25,716 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:25,728 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:25,729 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:25,730 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:25,730 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,730 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,730 - INFO: Starting regression paradigm
2025-07-10 16:58:25,730 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:25,731 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:25,853 - INFO: Fold 1 Training C-index: 0.6365
2025-07-10 16:58:25,865 - INFO: Fold 1 Test C-index: 0.5224
2025-07-10 16:58:25,866 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:25,877 - INFO: Fold 1 Validation C-index: 0.5049
2025-07-10 16:58:25,877 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:25,891 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:25,893 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:25,893 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:25,893 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,893 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:25,893 - INFO: Starting regression paradigm
2025-07-10 16:58:25,894 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:25,894 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,016 - INFO: Fold 2 Training C-index: 0.6801
2025-07-10 16:58:26,028 - INFO: Fold 2 Test C-index: 0.4817
2025-07-10 16:58:26,028 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:26,039 - INFO: Fold 2 Validation C-index: 0.5534
2025-07-10 16:58:26,039 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:26,051 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:26,053 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:26,053 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:26,053 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,054 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,054 - INFO: Starting regression paradigm
2025-07-10 16:58:26,054 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:26,054 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,179 - INFO: Fold 3 Training C-index: 0.6786
2025-07-10 16:58:26,192 - INFO: Fold 3 Test C-index: 0.5029
2025-07-10 16:58:26,192 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:26,203 - INFO: Fold 3 Validation C-index: 0.4757
2025-07-10 16:58:26,203 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:26,215 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:26,216 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:26,217 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:26,217 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,217 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,217 - INFO: Starting regression paradigm
2025-07-10 16:58:26,217 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:26,217 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,337 - INFO: Fold 4 Training C-index: 0.6313
2025-07-10 16:58:26,349 - INFO: Fold 4 Test C-index: 0.5570
2025-07-10 16:58:26,350 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:26,361 - INFO: Fold 4 Validation C-index: 0.4563
2025-07-10 16:58:26,361 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:26,374 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:26,376 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:26,376 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:26,377 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,377 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,377 - INFO: Starting regression paradigm
2025-07-10 16:58:26,377 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:26,377 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,493 - INFO: Fold 5 Training C-index: 0.6600
2025-07-10 16:58:26,506 - INFO: Fold 5 Test C-index: 0.4763
2025-07-10 16:58:26,506 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:26,516 - INFO: Fold 5 Validation C-index: 0.4854
2025-07-10 16:58:26,517 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:26,517 - INFO: C-indices per fold: ['0.5049', '0.5534', '0.4757', '0.4563', '0.4854']
2025-07-10 16:58:26,517 - INFO: Mean C-index: 0.4951
2025-07-10 16:58:26,517 - INFO: Std Dev of C-index: 0.0331
2025-07-10 16:58:26,517 - INFO: Worst C-index: 0.4563
2025-07-10 16:58:26,518 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-10 16:58:26,518 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-10 16:58:26,537 - INFO: Successfully loaded data from processed_equipment_data.csv. Shape: (598, 187)
2025-07-10 16:58:26,539 - INFO: Data shape after cleaning: (598, 182)
2025-07-10 16:58:26,540 - INFO: Succsessfully loaded feature columns. Total: 64
2025-07-10 16:58:26,540 - INFO: Succsessfully get feature columns. Total: 64
2025-07-10 16:58:26,543 - INFO: --- Fold 1/5 ---
2025-07-10 16:58:26,555 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:26,556 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:26,557 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:26,557 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,557 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,557 - INFO: Starting regression paradigm
2025-07-10 16:58:26,557 - INFO: Training backend model 'Weighted_cox' on fold 1...
2025-07-10 16:58:26,557 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,680 - INFO: Fold 1 Training C-index: 0.6383
2025-07-10 16:58:26,691 - INFO: Fold 1 Test C-index: 0.4857
2025-07-10 16:58:26,692 - INFO: Evaluating model on validation set of fold 1...
2025-07-10 16:58:26,703 - INFO: Fold 1 Validation C-index: 0.5789
2025-07-10 16:58:26,703 - INFO: --- Fold 2/5 ---
2025-07-10 16:58:26,715 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:26,717 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:26,717 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:26,717 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,718 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,718 - INFO: Starting regression paradigm
2025-07-10 16:58:26,718 - INFO: Training backend model 'Weighted_cox' on fold 2...
2025-07-10 16:58:26,718 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,838 - INFO: Fold 2 Training C-index: 0.6251
2025-07-10 16:58:26,851 - INFO: Fold 2 Test C-index: 0.5714
2025-07-10 16:58:26,851 - INFO: Evaluating model on validation set of fold 2...
2025-07-10 16:58:26,862 - INFO: Fold 2 Validation C-index: 0.5263
2025-07-10 16:58:26,862 - INFO: --- Fold 3/5 ---
2025-07-10 16:58:26,874 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:26,875 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:26,876 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:26,876 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,876 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:26,876 - INFO: Starting regression paradigm
2025-07-10 16:58:26,876 - INFO: Training backend model 'Weighted_cox' on fold 3...
2025-07-10 16:58:26,876 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:26,992 - INFO: Fold 3 Training C-index: 0.6736
2025-07-10 16:58:27,005 - INFO: Fold 3 Test C-index: 0.4708
2025-07-10 16:58:27,005 - INFO: Evaluating model on validation set of fold 3...
2025-07-10 16:58:27,015 - INFO: Fold 3 Validation C-index: 0.5158
2025-07-10 16:58:27,015 - INFO: --- Fold 4/5 ---
2025-07-10 16:58:27,025 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:27,026 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:27,026 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:27,026 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:27,027 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:27,027 - INFO: Starting regression paradigm
2025-07-10 16:58:27,027 - INFO: Training backend model 'Weighted_cox' on fold 4...
2025-07-10 16:58:27,027 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:27,146 - INFO: Fold 4 Training C-index: 0.6630
2025-07-10 16:58:27,160 - INFO: Fold 4 Test C-index: 0.5613
2025-07-10 16:58:27,160 - INFO: Evaluating model on validation set of fold 4...
2025-07-10 16:58:27,170 - INFO: Fold 4 Validation C-index: 0.5263
2025-07-10 16:58:27,170 - INFO: --- Fold 5/5 ---
2025-07-10 16:58:27,183 - INFO: Features for this fold have been standardized.
2025-07-10 16:58:27,184 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-10 16:58:27,184 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-10 16:58:27,185 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:27,185 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-10 16:58:27,185 - INFO: Starting regression paradigm
2025-07-10 16:58:27,185 - INFO: Training backend model 'Weighted_cox' on fold 5...
2025-07-10 16:58:27,186 - INFO: Using penalizer 0.21577931514746537 for Weighted_cox
2025-07-10 16:58:27,309 - INFO: Fold 5 Training C-index: 0.6554
2025-07-10 16:58:27,322 - INFO: Fold 5 Test C-index: 0.4286
2025-07-10 16:58:27,322 - INFO: Evaluating model on validation set of fold 5...
2025-07-10 16:58:27,334 - INFO: Fold 5 Validation C-index: 0.6421
2025-07-10 16:58:27,334 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-10 16:58:27,334 - INFO: C-indices per fold: ['0.5789', '0.5263', '0.5158', '0.5263', '0.6421']
2025-07-10 16:58:27,334 - INFO: Mean C-index: 0.5579
2025-07-10 16:58:27,334 - INFO: Std Dev of C-index: 0.0475
2025-07-10 16:58:27,334 - INFO: Worst C-index: 0.5158
2025-07-10 16:58:27,335 - INFO: ==================================================
2025-07-10 16:58:27,335 - INFO: Final Summary Over 10 Independent Run(s), validation set (GEHC's data)
2025-07-10 16:58:27,335 - INFO: ==================================================
2025-07-10 16:58:27,335 - INFO: Average of 'mean_c_index' over 10 runs: 0.5071 (Std Dev: 0.0457)
2025-07-10 16:58:27,336 - INFO: Average of 'std_c_index' over 10 runs: 0.0552 (Std Dev: 0.0228)
2025-07-10 16:58:27,336 - INFO: Average of 'worst_c_index' over 10 runs: 0.4344 (Std Dev: 0.0642)
2025-07-10 16:58:27,336 - INFO: ==================================================
2025-07-10 16:58:27,336 - INFO: Final Summary Over 10 Independent Run(s), test set (GEHC's generated data)
2025-07-10 16:58:27,336 - INFO: ==================================================
2025-07-10 16:58:27,336 - INFO: Average of 'mean_c_index_test' over 10 runs: 0.5130 (Std Dev: 0.0174)
2025-07-10 16:58:27,336 - INFO: Average of 'std_c_index_test' over 10 runs: 0.0491 (Std Dev: 0.0136)
2025-07-10 16:58:27,336 - INFO: Average of 'worst_c_index_test' over 10 runs: 0.4493 (Std Dev: 0.0295)
