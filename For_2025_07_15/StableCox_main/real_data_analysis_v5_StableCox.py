# 导入必要的库
from data.selection_bias import gen_selection_bias_data
from algorithm.DWR import DWR
from algorithm.SRDO import SRDO
from model.linear import get_algorithm_class
from metrics import get_metric_class
from utils import setup_seed, get_beta_s, get_expname, calc_var, pretty, get_cov_mask, BV_analysis
from Logger import Logger
from model.STG import STG
from sksurv.metrics import brier_score, cumulative_dynamic_auc
from sklearn.metrics import mean_squared_error, f1_score, r2_score
import numpy as np
import argparse
import os
import torch
from collections import defaultdict as dd
import pandas as pd

from sklearn.preprocessing import StandardScaler

from lifelines import CoxPHFitter
import seaborn as sns
import matplotlib.pyplot as plt
from lifelines.statistics import logrank_test
from lifelines import KaplanMeierFitter
from sksurv.util import Surv
from lifelines.utils import concordance_index
from sklearn.metrics import roc_auc_score
from sklearn.metrics import accuracy_score
import logging
# 新增导入
from sklearn.model_selection import StratifiedKFold, train_test_split

import warnings

warnings.filterwarnings('ignore')

# --- 定义常量 ---
duration_col = 'duration'
event_col = 'event'
SN_col = 'SN_Common'


def run_external_optimization(args, round, logger, device):
    """
    为外部优化器（如Optuna）提供的简化版本
    返回单个优化目标值
    """
    # 简化的数据加载和预处理
    PATH = r'processed_equipment_data_new.csv'
    try:
        data = pd.read_csv(PATH)
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}.")
        return {"optimization_objective": -1.0}  # 返回一个很差的分数
    
    # 数据预处理
    if SN_col in data.columns: 
        data = data.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    cols_to_drop = [col for col in problematic_cols if col in data.columns]
    data = data.drop(columns=cols_to_drop)
    
    feature_cols = pd.read_csv(r'../Stable_Cox_Proj/feature_names.csv')['feature_name'].values.tolist()
    feature_cols = [col for col in feature_cols if col in data.columns]
    
    if not feature_cols:
        return {"optimization_objective": -1.0}
    
    # 数据划分
    train_data, val_data = train_test_split(data, test_size=0.1, random_state=args.seed + round, stratify=data[event_col])
    
    # 交叉验证
    skf = StratifiedKFold(n_splits=args.n_splits, shuffle=True, random_state=args.seed + round)
    X, y = train_data[feature_cols], train_data[event_col]
    cv_scores = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        train_fold = train_data.iloc[train_idx]
        val_fold = train_data.iloc[val_idx]  # 交叉验证中的验证集
        
        # 标准化
        scaler = StandardScaler()
        scaler.fit(train_fold[feature_cols])
        train_fold_scaled = train_fold.copy()
        val_fold_scaled = val_fold.copy()
        train_fold_scaled[feature_cols] = scaler.transform(train_fold[feature_cols])
        val_fold_scaled[feature_cols] = scaler.transform(val_fold[feature_cols])
        
        # 权重计算
        n_fold = len(train_fold)
        W = np.ones((n_fold, 1))
        if args.reweighting == "SRDO":
            p_s_fold = len(feature_cols) // 2
            W = SRDO(train_fold_scaled[feature_cols].values, p_s_fold, 
                    hidden_layer_sizes=(4,4), 
                    decorrelation_type=args.decorrelation_type,
                    max_iter=args.iters_balance)
        
        # 归一化权重
        if args.reweighting != "None":
            mean_value = max(np.mean(W), 1e-8)
            W = W * (1 / mean_value)
            W = np.clip(W, 1.5702480768685312e-06, 4.1)
        
        # 训练模型
        train_fold_scaled['Weights'] = W.flatten()
        cph = CoxPHFitter(penalizer=args.lam_backend)
        cph.fit(train_fold_scaled[feature_cols + [duration_col, event_col, 'Weights']], 
               duration_col=duration_col, 
               event_col=event_col,
               weights_col='Weights')
        
        # 在交叉验证的验证集上评估
        val_fold_scaled['Weights'] = 1.0
        fold_val_c_index = concordance_index(
            val_fold_scaled[duration_col],
            -cph.predict_partial_hazard(val_fold_scaled),
            val_fold_scaled[event_col]
        )
        cv_scores.append(fold_val_c_index)
    
    # 返回平均CV分数作为优化目标
    mean_cv_score = np.mean(cv_scores)
    return {"optimization_objective": mean_cv_score}


def get_args():
    parser = argparse.ArgumentParser(description="Script to launch sample reweighting experiments",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)

    # data generation
    parser.add_argument("--p", type=int, default=10, help="Input dim")
    parser.add_argument("--n", type=int, default=2000, help="Sample size")
    parser.add_argument("--V_ratio", type=float, default=0.5)
    parser.add_argument("--Vb_ratio", type=float, default=0.1)
    parser.add_argument("--true_func", choices=["linear", ], default="linear")
    parser.add_argument("--mode", choices=["S_|_V", "S->V", "V->S", "collinearity"], default="S_|_V")
    parser.add_argument("--misspe", choices=["poly", "exp", "None"], default="poly")
    parser.add_argument("--corr_s", type=float, default=0.9)
    parser.add_argument("--corr_v", type=float, default=0.1)
    parser.add_argument("--mms_strength", type=float, default=1.0, help="model misspecifction strength")
    parser.add_argument("--spurious", choices=["nonlinear", "linear"], default="nonlinear")
    parser.add_argument("--r_train", type=float, default=2.5, help="Input dim")
    parser.add_argument("--r_list", type=float, nargs="+", default=[-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3])
    parser.add_argument("--noise_variance", type=float, default=0.3)

    # frontend reweighting
    parser.add_argument("--reweighting", choices=["None", "DWR", "SRDO"], default="SRDO")
    parser.add_argument("--decorrelation_type", choices=["global", "group"], default="global")
    parser.add_argument("--order", type=int, default=1)
    parser.add_argument("--iters_balance", type=int, default=4000)

    parser.add_argument("--topN", type=int, default=26)
    # backend model
    parser.add_argument("--backend",
                        choices=["OLS", "Lasso", "Ridge", "Weighted_cox", "LogLogistic", "Weibull", "LogNormal"],
                        default="Weighted_cox")
    parser.add_argument("--paradigm", choices=["regr", "fs", ], default="regr")
    parser.add_argument("--iters_train", type=int, default=5000)
    parser.add_argument("--lam_backend", type=float, default=0.9859521585310984)  # regularizer coefficient for 1st Cox
    parser.add_argument("--lam_backend2", type=float, default=2.7712399316485526e-05)  # regularizer coefficient for 2rd Cox
    parser.add_argument("--external_optimization", action="store_true", help="Flag for external optimization (e.g., Optuna)")
    parser.add_argument("--fs_type", choices=["oracle", "None", "given", "STG"], default="STG")
    parser.add_argument("--mask_given", type=int, nargs="+", default=[1, 1, 1, 1, 1, 0, 0, 0, 0, 0])
    parser.add_argument("--mask_threshold", type=float, default=0.2)
    parser.add_argument("--lam_STG", type=float, default=3)
    parser.add_argument("--sigma_STG", type=float, default=0.1)
    parser.add_argument("--metrics", nargs="+", default=["L1_beta_error", "L2_beta_error"])
    parser.add_argument("--bv_analysis", action="store_true")

    # others
    parser.add_argument("--seed", type=int, default=2)
    parser.add_argument("--times", type=int, default=10)
    parser.add_argument("--result_dir", default="results")

    # --- 新增参数 ---
    parser.add_argument("--n_splits", type=int, default=10, help="Number of folds for cross-validation")

    return parser.parse_args()


def main(args, round, logger):
    logging.info(f"--- Starting Round {round} with Seed {args.seed + round} ---")
    setup_seed(args.seed + round)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 如果是外部优化，返回简化的结果
    if args.external_optimization:
        return run_external_optimization(args, round, logger, device)

    # --- 1. 数据加载和初始预处理 ---
    PATH = r'processed_equipment_data_new.csv'
    # PATH = r'processed_equipment_data_all.csv'
    try:
        data = pd.read_csv(PATH)
        logging.info(f"Successfully loaded data from {PATH}. Shape: {data.shape}")
    except FileNotFoundError:
        logging.error(f"Data file not found at {PATH}. Please check path.")
        return {}  # 返回空字典以避免在主循环中出错

    if SN_col in data.columns: data = data.drop(columns=[SN_col])
    problematic_cols = ['0A_Flattop', 'Ambient_HST_delta', 'Ambient_temp', 'Offset_cal']
    # problematic_cols = []
    cols_to_drop = [col for col in problematic_cols if col in data.columns]
    data = data.drop(columns=cols_to_drop)
    # feature_cols = pd.read_csv(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_07_15\selected_features.csv')[
    #     'features'][0]  # 这个是decorrelation的结果
    # feature_cols = eval(feature_cols)
    # feature_cols = pd.read_csv(r'../Stable_Cox_Proj/feature_names.csv')['feature_name'].values.tolist() # 这个是STG的结果
    # feature_cols = [col for col in feature_cols if col in data.columns]
    p = data.shape[1] - 2

    feature_cols = [col for col in data.columns if col not in [duration_col, event_col]]

    if feature_cols:
        logging.info(f"Succsessfully get feature columns. Total: {len(feature_cols)}")
    else:
        logging.error("No feature columns could be identified. Exiting.")
        return {}  # 返回空字典以避免在主循环中出错
    # --- 拆出来train 和 val
    train_data, test_data = train_test_split(data, test_size=0.1, random_state=args.seed + round, stratify=data[event_col])
    # train_data, test_data = data[data['sampled_pool'] == 1].drop(columns=['generate', 'sampled_pool']), data[data['sampled_pool'] == 0].drop(columns=['generate', 'sampled_pool'])
    # print(f"train_data shape: {train_data.shape}, test_data shape: {test_data.shape}")
    # feature_cols.remove('sampled_pool')
    # feature_cols.remove('generate')
    # --- 2. 设置交叉验证 ---
    skf = StratifiedKFold(n_splits=args.n_splits, shuffle=True, random_state=args.seed + round)
    X, y = train_data[feature_cols], train_data[event_col]
    cv_val_c_indices = []  # 交叉验证中的验证集c-index
    cv_fold_val_c_indices = []  # 交叉验证中每个fold的验证集c-index
    best_cv_model = None  # 存储交叉验证中最好的模型
    best_cv_score = -np.inf  # 存储交叉验证中最好的分数

    # --- 3. 开始交叉验证循环 ---
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logging.info(f"--- Fold {fold + 1}/{args.n_splits} ---")

        # training_pd_data_fold = data.iloc[train_idx].copy()
        # test_pd_data_fold = data.iloc[val_idx].copy()

        train_data_fold = train_data.iloc[train_idx]
        val_data_fold = train_data.iloc[val_idx]  # 这是交叉验证中的验证集，不是测试集

        # --- 新增：数据标准化 ---
        scaler = StandardScaler()

        # 1. 只在当前折的训练集上 fit scaler
        #    注意：我们只对特征列进行标准化
        scaler.fit(train_data_fold[feature_cols])

        # 2. 创建副本以避免 SettingWithCopyWarning
        training_pd_data_fold = train_data_fold.copy()
        val_pd_data_fold = val_data_fold.copy()  # 交叉验证中的验证集

        # 3. 使用 fit 好的 scaler 来 transform 训练集和验证集
        training_pd_data_fold[feature_cols] = scaler.transform(train_data_fold[feature_cols])
        val_pd_data_fold[feature_cols] = scaler.transform(val_data_fold[feature_cols])
        logging.info("Features for this fold have been standardized.")

        X_train_fold_np = training_pd_data_fold[feature_cols].values
        n_fold, p_fold = X_train_fold_np.shape

        # --- 4. Reweighting ---
        W = np.ones((n_fold, 1))
        if args.reweighting == "DWR":
            # 假设DWR返回 (n, 1) 的numpy数组
            W = DWR(X_train_fold_np, logger=logger, device=device)
        elif args.reweighting == "SRDO":
            logging.info("Using SRDO reweighting")
            p_s_fold = p_fold // 2
            # 假设SRDO返回 (n, 1) 的numpy数组
            W = SRDO(X_train_fold_np, p_s_fold, hidden_layer_sizes=(4,4), decorrelation_type=args.decorrelation_type,
                     max_iter=args.iters_balance)
            logging.info("SRDO reweighting completed.")

        if args.reweighting != "None":
            logging.info(f"Weights calculated and normalized for fold {fold + 1}.")

        logging.info("Weight statistics - mean: %.6f, min: %.6f, max: %.6f", np.mean(W), np.min(W), np.max(W))
        mean_value = max(np.mean(W), 1e-8)
        W = W * (1 / mean_value)
        logging.info("After normalization - max: %.6f, min: %.6f", np.max(W), np.min(W))

        logging.info(f"W before clip: min={W.min():.6f}, max={W.max():.6f}, mean={W.mean():.6f}, std={W.std():.6f}")

        # clip之后
        W = np.clip(W,  1.5702480768685312e-06, 4.1)
        logging.info(f"W after clip: min={W.min():.6f}, max={W.max():.6f}, mean={W.mean():.6f}, std={W.std():.6f}")

        # --- 5. 模型训练 ---
        if args.paradigm == "regr":
            logging.info("Starting regression paradigm")
            logging.info(f"Training backend model '{args.backend}' on fold {fold + 1}...")
            mask = [True, ] * p
            if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
                logging.info(f"Using penalizer {args.lam_backend} for Weighted_cox")
                model_func = get_algorithm_class(args.backend)
                model = model_func(
                    X=training_pd_data_fold[feature_cols + [duration_col, event_col]],
                    duration_col=duration_col,
                    event_col=event_col,
                    W=W,
                    pen=args.lam_backend,
                    **vars(args)
                )
                train_c_index = model.concordance_index_
                logging.info(f"Fold {fold + 1} Training C-index: {train_c_index:.4f}")

            # elif args.backend in ["OLS", "Lasso", "Ridge"]:
            #     model_func = get_algorithm_class(args.backend)
            #     model = model_func(
            #         X=training_pd_data_fold[feature_cols],
            #         Y=training_pd_data_fold[[duration_col, event_col]],
            #         W=W,
            #         **vars(args)
            #     )
            #     train_c_index = model.score(training_pd_data_fold, scoring_method='concordance_index')
            #     logging.info(f"Fold {fold + 1} Training C-index: {train_c_index:.4f}")
            else:
                raise NotImplementedError(f"Backend '{args.backend}' not implemented for regression paradigm.")

            # --- CORRECTION PART 1: 评估训练集分数 ---
            # 在评估前，必须为DataFrame添加模型期望的'Weights'列

        else:
            raise NotImplementedError(f"Paradigm '{args.paradigm}' requires adaptation for CV.")

        if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
            summary = model.summary
            # print("summary", summary)
            if args.topN is not None and args.topN > 0:
                sorted_indices = summary['p'].sort_values().head(args.topN).index
            else:
                # 自动选择所有p-value小于0.05的特征
                sorted_indices = summary[summary['p'] < summary['p'].quantile(0.1)].index


            # 只选择sorted_indices中选择的特征
            # feature_cols = list(sorted_indices)
            # feature_cols = \
            # pd.read_csv(r'C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_07_07\selected_features.csv')[
            #     'features'][3]
            # feature_cols = eval(feature_cols)
            # print(f"Number of selected features: {len(feature_cols)}")
            # if args.backend in ["LogLogistic", "Weibull", "LogNormal"]:
            #     feature_cols = [col[1] for col in feature_cols[1:]]
            #     feature_cols = list(set(feature_cols))
            # print(f"Selected features {len(feature_cols)}, top 5: {feature_cols[:5]}")

            val_pd_data_fold["Weights"] = np.ones((val_pd_data_fold.shape[0],))
            # val_data_for_score = val_pd_data_fold[feature_cols + [duration_col, event_col]]
            val_data_for_score = val_pd_data_fold[feature_cols + [duration_col, event_col, "Weights"]]

            # training_data_for_score['Weights'] = W.flatten()  # 使用训练时的权重
            # train_c_index = model.score(training_data_for_score, scoring_method='concordance_index')

            if args.backend in ["Weighted_cox"]:
                # cph = CoxPHFitter(penalizer=args.lam_backend2)
                # cph.fit(training_pd_data_fold[feature_cols + [duration_col, event_col]],
                #         duration_col=duration_col, event_col=event_col)
                fold_val_c_index = concordance_index(val_data_for_score[duration_col], -model.predict_partial_hazard(val_data_for_score), val_data_for_score[event_col])

            elif args.backend in ["LogLogistic", "Weibull", "LogNormal"]:
                # model_func = get_algorithm_class(args.backend)
                # model = model_func(
                #     X=val_pd_data_fold,
                #     duration_col=duration_col,
                #     event_col=event_col,
                #     pen=args.lam_backend,
                #     **vars(args)
                # )
                fold_val_c_index = model.score(val_data_for_score, scoring_method='concordance_index')
            logging.info(f"Fold {fold + 1} CV Validation C-index: {fold_val_c_index:.4f}")
            cv_fold_val_c_indices.append(fold_val_c_index)

            avg_score = train_c_index*0.3 + fold_val_c_index*0.7

            # 5 FOLD, avg_score is the best of 5 fold

            # --- 6. 模型评估 ---
            logging.info(f"Evaluating model on validation set of fold {fold + 1}...")
            
            # 更新最佳模型（基于交叉验证中的验证集性能）
            if avg_score > best_cv_score:
                best_cv_score = avg_score
                best_cv_model = model
                logging.info(f"Fold {fold + 1}: New best CV model with fold validation C-index: {fold_val_c_index:.4f}")

        # elif args.backend in ["OLS", "Lasso", "Ridge"]:
        #     r2_scores = []
        #     rmses = []
        #     mapes = []
        #     f1_scores = []
        #     training_data_for_score = training_pd_data_fold.copy()
        #     # 使用duration和event作为Y值进行训练
        #     # 将W作为特征的一部分
        #     training_features = np.concatenate((training_data_for_score[feature_cols], W), axis=1)
        #     model.fit(training_features,
        #               training_data_for_score[[duration_col, event_col]])
        #
        #     # 分别计算duration和event的预测结果 - 使用包含权重的特征
        #     train_features_with_weights = np.concatenate((training_data_for_score[feature_cols], W), axis=1)
        #     train_pred = model.predict(train_features_with_weights)
        #     train_duration_pred = train_pred[:, 0]  # 第一列是duration的预测
        #     train_event_pred = train_pred[:, 1]  # 第二列是event的预测
        #
        #     # 计算训练集的评估指标 - 不再使用sample_weight
        #     train_r2 = r2_score(training_data_for_score[duration_col], train_duration_pred)
        #     train_rmse = np.sqrt(mean_squared_error(training_data_for_score[duration_col], train_duration_pred))
        #     train_mape = np.mean(np.abs((training_data_for_score[duration_col] - train_duration_pred) /
        #                                 training_data_for_score[duration_col]))
        #
        #     # F1 score计算
        #     f1_scores.append(f1_score(training_data_for_score[event_col], train_event_pred > 0.5))
        #
        #     logging.info(
        #         f"Fold {fold + 1} Training R2: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAPE: {train_mape:.4f}, F1: {f1_scores[-1]:.4f}")
        #
        #     # 交叉验证中的验证集评估 - 使用全1权重
        #     Weights = np.ones((val_pd_data_fold.shape[0], 1))
        #     val_features_with_weights = np.concatenate((val_pd_data_fold[feature_cols], Weights), axis=1)
        #     val_pred = model.predict(val_features_with_weights)
        #     val_duration_pred = val_pred[:, 0]
        #     val_event_pred = val_pred[:, 1]
        #
        #     # 交叉验证中的验证集评估指标计算
        #     fold_val_r2 = r2_score(val_pd_data_fold[duration_col], val_duration_pred)
        #     fold_val_rmse = np.sqrt(mean_squared_error(val_pd_data_fold[duration_col], val_duration_pred))
        #     fold_val_mape = np.mean(np.abs((val_pd_data_fold[duration_col] - val_duration_pred) /
        #                               val_pd_data_fold[duration_col]))
        #     fold_val_f1 = f1_score(val_pd_data_fold[event_col], val_event_pred > 0.5)
        #
        #     r2_scores.append(fold_val_r2)
        #     rmses.append(fold_val_rmse)
        #     mapes.append(fold_val_mape)
        #     f1_scores.append(fold_val_f1)
        #     logging.info(
        #         f"Fold {fold + 1} CV Validation R2: {fold_val_r2:.4f}, RMSE: {fold_val_rmse:.4f}, MAPE: {fold_val_mape:.4f}, F1: {fold_val_f1:.4f}")

    # --- 7. 聚合交叉验证结果 ---
    if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:


        mean_cv_fold_val_c_index, std_cv_fold_val_c_index, worst_cv_fold_val_c_index = np.mean(cv_fold_val_c_indices), np.std(cv_fold_val_c_indices), np.min(cv_fold_val_c_indices)
        
        logging.info("--- Cross-Validation Summary for this Round ---")
        logging.info(f"CV Fold Validation C-indices per fold: {[f'{c:.4f}' for c in cv_fold_val_c_indices]}")
        logging.info(f"Mean CV Fold Validation C-index: {mean_cv_fold_val_c_index:.4f}")
        logging.info(f"Std Dev of CV Fold Validation C-index: {std_cv_fold_val_c_index:.4f}")
        logging.info(f"Worst CV Fold Validation C-index: {worst_cv_fold_val_c_index:.4f}")
        
        # --- 8. 使用最佳模型在最终测试集上评估 ---
        if best_cv_model is not None:
            logging.info("--- Final Test Set Evaluation with Best CV Model ---")
            
            # 准备最终测试集数据（使用与训练相同的标准化）
            final_test_data = test_data[feature_cols + [duration_col, event_col]].copy()
            final_test_data["Weights"] = np.ones((final_test_data.shape[0],))

            mean = np.mean(final_test_data[feature_cols], axis=0)
            std = np.std(final_test_data[feature_cols], axis=0)
            final_test_data[feature_cols] = (final_test_data[feature_cols] - mean) / std

            # 使用最佳模型进行预测
            final_test_c_index = concordance_index(
                final_test_data[duration_col], 
                -best_cv_model.predict_partial_hazard(final_test_data), 
                final_test_data[event_col]
            )
            
            logging.info(f"Final Test Set C-index: {final_test_c_index:.4f}")
            logging.info(f"Best CV Model Validation C-index: {best_cv_score:.4f}")
        else:
            logging.warning("No best CV model found for final test evaluation.")
            final_test_c_index = None

        return {
            "mean_cv_fold_val_c_index": mean_cv_fold_val_c_index,
            "std_cv_fold_val_c_index": std_cv_fold_val_c_index,
            "worst_cv_fold_val_c_index": worst_cv_fold_val_c_index,
            "best_cv_score": best_cv_score,
            "final_test_c_index": final_test_c_index
        }


    #
    # elif args.backend in ["OLS", "Lasso", "Ridge"]:
    #     if not r2_scores:
    #         logging.warning("No folds were processed. Returning empty results.")
    #         return {}
    #
    #     mean_r2, std_r2, worst_r2 = np.mean(r2_scores), np.std(r2_scores), np.min(r2_scores)
    #     mean_rmse, std_rmse, worst_rmse = np.mean(rmses), np.std(rmses), np.min(rmses)
    #     mean_mape, std_mape, worst_mape = np.mean(mapes), np.std(mapes), np.min(mapes)
    #     mean_f1, std_f1, worst_f1 = np.mean(f1_scores), np.std(f1_scores), np.min(f1_scores)
    #
    #     logging.info("--- Cross-Validation Summary for this Round ---")
    #     logging.info(
    #         f"R2 scores per fold: {[f'{r2:.4f}' for r2 in r2_scores]}, Mean: {mean_r2:.4f}, Std: {std_r2:.4f}, Worst: {worst_r2:.4f}")
    #     logging.info(
    #         f"RMSE scores per fold: {[f'{rmse:.4f}' for rmse in rmses]}, Mean: {mean_rmse:.4f}, Std: {std_rmse:.4f}, Worst: {worst_rmse:.4f}")
    #     logging.info(
    #         f"MAPE scores per fold: {[f'{mape:.4f}' for mape in mapes]}, Mean: {mean_mape:.4f}, Std: {std_mape:.4f}, Worst: {worst_mape:.4f}")
    #     logging.info(
    #         f"F1 scores per fold: {[f'{f1:.4f}' for f1 in f1_scores]}, Mean: {mean_f1:.4f}, Std: {std_f1:.4f}, Worst: {worst_f1:.4f}")
    #
    #     return {"mean_r2": mean_r2, "std_r2": std_r2, "worst_r2": worst_r2,
    #             "mean_rmse": mean_rmse, "std_rmse": std_rmse, "worst_rmse": worst_rmse,
    #             "mean_mape": mean_mape, "std_mape": std_mape, "worst_mape": worst_mape,
    #             "mean_f1": mean_f1, "std_f1": std_f1, "worst_f1": worst_f1}


if __name__ == "__main__":
    import time

    begin_time = time.time()
    args = get_args()
    setup_seed(args.seed)
    expname = get_expname(args)
    os.makedirs(os.path.join(args.result_dir, expname), exist_ok=True)
    logger = Logger(args)
    logger.log_args(args)

    results_list = dd(list)
    for i in range(args.times):
        logger.info(
            "\n \n =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round %d =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=" % i)
        results_per_run = main(args, i, logger)
        if results_per_run:  # 确保main函数成功运行
            for k, v in results_per_run.items():
                results_list[k].append(v)

    # 将所欲的results_list保存到csv文件中
    # with open(os.path.join(r"C:\Users\<USER>\OneDrive - CentraleSupelec\2025_Doc\For_2025_06_23", 'results.csv'), 'w') as f:
    #     for k, v in results_list.items():
    #         f.write(f"{k},{v}\n")
    if args.backend in ["Weighted_cox", "LogLogistic", "Weibull", "LogNormal"]:
        if results_list:
            logging.info("=" * 50)
            logging.info(f"Final Summary Over {args.times} Independent Run(s)")
            logging.info("=" * 50)

            # 遍历聚合后的结果
            for metric_name, value_list in results_list.items():
                # 计算多次运行的平均值和标准差
                final_mean = np.mean(value_list)
                final_std = np.std(value_list)

                # 打印格式化的结果
                logging.info(
                    f"Average of '{metric_name}' over {args.times} runs: {final_mean:.4f} (Std Dev: {final_std:.4f})")
            
            # 特别突出显示最终测试集的结果
            if 'final_test_c_index' in results_list:
                final_test_scores = results_list['final_test_c_index']
                logging.info("=" * 50)
                logging.info("Final Test Set Performance Summary:")
                logging.info(f"Final Test C-indices: {[f'{s:.4f}' for s in final_test_scores]}")
                logging.info(f"Mean Final Test C-index: {np.mean(final_test_scores):.4f} (±{np.std(final_test_scores):.4f})")
                logging.info(f"Best Final Test C-index: {np.max(final_test_scores):.4f}")
                logging.info(f"Worst Final Test C-index: {np.min(final_test_scores):.4f}")
                logging.info("=" * 50)
        else:
            logging.info("No results were generated from the runs.")

    elif args.backend in ["OLS", "Lasso", "Ridge"]:
        if results_list:
            logging.info("=" * 50)
            logging.info(f"Final Summary Over {args.times} Independent Run(s)")
            logging.info("=" * 50)

            # 遍历聚合后的结果，例如 'mean_r2'
            for metric_name, value_list in results_list.items():
                # 计算多次运行的平均值和标准差
                final_mean = np.mean(value_list)
                final_std = np.std(value_list)

                # 打印格式化的结果
                # 例如: "Average of 'mean_r2' over 10 runs: 0.5012 (Std Dev: 0.0050)"
                logging.info(
                    f"Average of '{metric_name}' over {args.times} runs: {final_mean:.4f} (Std Dev: {final_std:.4f})")
        else:
            logging.info("No results were generated from the runs.")
    print(f"Total time: {time.time() - begin_time:.2f} seconds")
