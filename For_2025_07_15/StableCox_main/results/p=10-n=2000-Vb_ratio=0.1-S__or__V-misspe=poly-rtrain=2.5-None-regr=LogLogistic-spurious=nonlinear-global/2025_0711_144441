2025-07-11 14:44:41,033 - INFO: p: 10
2025-07-11 14:44:41,033 - INFO: n: 2000
2025-07-11 14:44:41,033 - INFO: V_ratio: 0.5
2025-07-11 14:44:41,034 - INFO: Vb_ratio: 0.1
2025-07-11 14:44:41,034 - INFO: true_func: linear
2025-07-11 14:44:41,034 - INFO: mode: S_|_V
2025-07-11 14:44:41,034 - INFO: misspe: poly
2025-07-11 14:44:41,035 - INFO: corr_s: 0.9
2025-07-11 14:44:41,036 - INFO: corr_v: 0.1
2025-07-11 14:44:41,036 - INFO: mms_strength: 1.0
2025-07-11 14:44:41,036 - INFO: spurious: nonlinear
2025-07-11 14:44:41,036 - INFO: r_train: 2.5
2025-07-11 14:44:41,036 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-11 14:44:41,036 - INFO: noise_variance: 0.3
2025-07-11 14:44:41,036 - INFO: reweighting: None
2025-07-11 14:44:41,036 - INFO: decorrelation_type: global
2025-07-11 14:44:41,036 - INFO: order: 1
2025-07-11 14:44:41,036 - INFO: iters_balance: 2500
2025-07-11 14:44:41,036 - INFO: topN: 10
2025-07-11 14:44:41,036 - INFO: backend: LogLogistic
2025-07-11 14:44:41,036 - INFO: paradigm: regr
2025-07-11 14:44:41,036 - INFO: iters_train: 5000
2025-07-11 14:44:41,036 - INFO: lam_backend: 0.03
2025-07-11 14:44:41,036 - INFO: lam_backend2: 0.03
2025-07-11 14:44:41,036 - INFO: fs_type: STG
2025-07-11 14:44:41,036 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-11 14:44:41,036 - INFO: mask_threshold: 0.2
2025-07-11 14:44:41,036 - INFO: lam_STG: 3
2025-07-11 14:44:41,036 - INFO: sigma_STG: 0.1
2025-07-11 14:44:41,037 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-11 14:44:41,037 - INFO: bv_analysis: False
2025-07-11 14:44:41,037 - INFO: seed: 2
2025-07-11 14:44:41,037 - INFO: times: 10
2025-07-11 14:44:41,037 - INFO: result_dir: results
2025-07-11 14:44:41,037 - INFO: n_splits: 5
2025-07-11 14:44:41,037 - INFO: 
2025-07-11 14:44:41,037 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:41,037 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-11 14:44:41,149 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:41,153 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:41,159 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:41,190 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:41,195 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:41,195 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:41,196 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:41,196 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:41,196 - INFO: Starting regression paradigm
2025-07-11 14:44:41,196 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:41,196 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:41,441 - INFO: p: 10
2025-07-11 14:44:41,441 - INFO: n: 2000
2025-07-11 14:44:41,441 - INFO: V_ratio: 0.5
2025-07-11 14:44:41,441 - INFO: Vb_ratio: 0.1
2025-07-11 14:44:41,441 - INFO: true_func: linear
2025-07-11 14:44:41,441 - INFO: mode: S_|_V
2025-07-11 14:44:41,441 - INFO: misspe: poly
2025-07-11 14:44:41,442 - INFO: corr_s: 0.9
2025-07-11 14:44:41,442 - INFO: corr_v: 0.1
2025-07-11 14:44:41,442 - INFO: mms_strength: 1.0
2025-07-11 14:44:41,442 - INFO: spurious: nonlinear
2025-07-11 14:44:41,442 - INFO: r_train: 2.5
2025-07-11 14:44:41,442 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-11 14:44:41,442 - INFO: noise_variance: 0.3
2025-07-11 14:44:41,442 - INFO: reweighting: None
2025-07-11 14:44:41,442 - INFO: decorrelation_type: global
2025-07-11 14:44:41,442 - INFO: order: 1
2025-07-11 14:44:41,442 - INFO: iters_balance: 2500
2025-07-11 14:44:41,442 - INFO: topN: 15
2025-07-11 14:44:41,442 - INFO: backend: LogLogistic
2025-07-11 14:44:41,442 - INFO: paradigm: regr
2025-07-11 14:44:41,442 - INFO: iters_train: 5000
2025-07-11 14:44:41,443 - INFO: lam_backend: 0.03
2025-07-11 14:44:41,443 - INFO: lam_backend2: 0.03
2025-07-11 14:44:41,443 - INFO: fs_type: STG
2025-07-11 14:44:41,443 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-11 14:44:41,443 - INFO: mask_threshold: 0.2
2025-07-11 14:44:41,443 - INFO: lam_STG: 3
2025-07-11 14:44:41,443 - INFO: sigma_STG: 0.1
2025-07-11 14:44:41,443 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-11 14:44:41,443 - INFO: bv_analysis: False
2025-07-11 14:44:41,443 - INFO: seed: 2
2025-07-11 14:44:41,443 - INFO: times: 10
2025-07-11 14:44:41,443 - INFO: result_dir: results
2025-07-11 14:44:41,443 - INFO: n_splits: 5
2025-07-11 14:44:41,443 - INFO: 
2025-07-11 14:44:41,443 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:41,443 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-11 14:44:41,531 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:41,535 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:41,541 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:41,571 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:41,576 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:41,576 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:41,576 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:41,576 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:41,576 - INFO: Starting regression paradigm
2025-07-11 14:44:41,576 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:41,576 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:42,005 - INFO: Fold 1 Training C-index: 0.5516
2025-07-11 14:44:42,208 - INFO: Fold 1 Test C-index: 0.4812
2025-07-11 14:44:42,209 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:42,219 - INFO: Fold 1 Validation C-index: 0.5101
2025-07-11 14:44:42,219 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:42,226 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:42,226 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:42,226 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:42,227 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,227 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,227 - INFO: Starting regression paradigm
2025-07-11 14:44:42,227 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:42,227 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:42,408 - INFO: Fold 1 Training C-index: 0.5516
2025-07-11 14:44:42,488 - INFO: Fold 2 Training C-index: 0.5277
2025-07-11 14:44:42,640 - INFO: Fold 1 Test C-index: 0.4795
2025-07-11 14:44:42,640 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:42,651 - INFO: Fold 1 Validation C-index: 0.5039
2025-07-11 14:44:42,651 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:42,658 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:42,659 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:42,659 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:42,659 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,659 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,659 - INFO: Starting regression paradigm
2025-07-11 14:44:42,659 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:42,659 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:42,702 - INFO: Fold 2 Test C-index: 0.5395
2025-07-11 14:44:42,702 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:42,712 - INFO: Fold 2 Validation C-index: 0.5110
2025-07-11 14:44:42,713 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:42,720 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:42,720 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:42,721 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:42,721 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,721 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,721 - INFO: Starting regression paradigm
2025-07-11 14:44:42,721 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:42,721 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:42,877 - INFO: Fold 2 Training C-index: 0.5290
2025-07-11 14:44:42,924 - INFO: Fold 3 Training C-index: 0.5270
2025-07-11 14:44:43,138 - INFO: Fold 2 Test C-index: 0.5452
2025-07-11 14:44:43,138 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:43,149 - INFO: Fold 2 Validation C-index: 0.5054
2025-07-11 14:44:43,149 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:43,157 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,157 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,157 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,158 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,158 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,158 - INFO: Starting regression paradigm
2025-07-11 14:44:43,158 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:43,158 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,180 - INFO: Fold 3 Test C-index: 0.5405
2025-07-11 14:44:43,180 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:43,190 - INFO: Fold 3 Validation C-index: 0.5080
2025-07-11 14:44:43,190 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:43,196 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,197 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,197 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,197 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,197 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,197 - INFO: Starting regression paradigm
2025-07-11 14:44:43,197 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:43,197 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,363 - INFO: Fold 3 Training C-index: 0.5279
2025-07-11 14:44:43,418 - INFO: Fold 4 Training C-index: 0.5298
2025-07-11 14:44:43,620 - INFO: Fold 3 Test C-index: 0.5410
2025-07-11 14:44:43,620 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:43,632 - INFO: Fold 3 Validation C-index: 0.5032
2025-07-11 14:44:43,632 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:43,640 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,641 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,641 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,642 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,642 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,642 - INFO: Starting regression paradigm
2025-07-11 14:44:43,642 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:43,642 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,647 - INFO: Fold 4 Test C-index: 0.5290
2025-07-11 14:44:43,647 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:43,659 - INFO: Fold 4 Validation C-index: 0.5122
2025-07-11 14:44:43,659 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:43,666 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,667 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,667 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,667 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,667 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,667 - INFO: Starting regression paradigm
2025-07-11 14:44:43,667 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:43,667 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,856 - INFO: Fold 4 Training C-index: 0.5321
2025-07-11 14:44:43,942 - INFO: Fold 5 Training C-index: 0.5244
2025-07-11 14:44:44,061 - INFO: Fold 4 Test C-index: 0.5256
2025-07-11 14:44:44,061 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:44,072 - INFO: Fold 4 Validation C-index: 0.5081
2025-07-11 14:44:44,072 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:44,080 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,081 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,081 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,081 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,081 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,081 - INFO: Starting regression paradigm
2025-07-11 14:44:44,081 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:44,081 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,136 - INFO: Fold 5 Test C-index: 0.5451
2025-07-11 14:44:44,136 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:44,146 - INFO: Fold 5 Validation C-index: 0.5075
2025-07-11 14:44:44,146 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:44,146 - INFO: C-indices per fold: ['0.5101', '0.5110', '0.5080', '0.5122', '0.5075']
2025-07-11 14:44:44,146 - INFO: Mean C-index: 0.5097
2025-07-11 14:44:44,146 - INFO: Std Dev of C-index: 0.0018
2025-07-11 14:44:44,146 - INFO: Worst C-index: 0.5075
2025-07-11 14:44:44,147 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:44,147 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-11 14:44:44,216 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:44,219 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:44,224 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:44,252 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,256 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,256 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,257 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,257 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,257 - INFO: Starting regression paradigm
2025-07-11 14:44:44,257 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:44,257 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,368 - INFO: Fold 5 Training C-index: 0.5314
2025-07-11 14:44:44,579 - INFO: Fold 5 Test C-index: 0.5373
2025-07-11 14:44:44,579 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:44,592 - INFO: Fold 5 Validation C-index: 0.5018
2025-07-11 14:44:44,592 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:44,592 - INFO: C-indices per fold: ['0.5039', '0.5054', '0.5032', '0.5081', '0.5018']
2025-07-11 14:44:44,592 - INFO: Mean C-index: 0.5045
2025-07-11 14:44:44,592 - INFO: Std Dev of C-index: 0.0022
2025-07-11 14:44:44,592 - INFO: Worst C-index: 0.5018
2025-07-11 14:44:44,593 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:44,593 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-11 14:44:44,660 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:44,664 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:44,670 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:44,700 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,704 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,705 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,705 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,705 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,705 - INFO: Starting regression paradigm
2025-07-11 14:44:44,705 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:44,705 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:45,052 - INFO: Fold 1 Training C-index: 0.5358
2025-07-11 14:44:45,324 - INFO: Fold 1 Test C-index: 0.4959
2025-07-11 14:44:45,325 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:45,342 - INFO: Fold 1 Validation C-index: 0.5007
2025-07-11 14:44:45,342 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:45,355 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:45,357 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:45,357 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:45,357 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:45,358 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:45,358 - INFO: Starting regression paradigm
2025-07-11 14:44:45,358 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:45,358 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:45,715 - INFO: Fold 2 Training C-index: 0.5241
2025-07-11 14:44:46,445 - INFO: Fold 2 Test C-index: 0.5295
2025-07-11 14:44:46,446 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:46,470 - INFO: Fold 2 Validation C-index: 0.5048
2025-07-11 14:44:46,470 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:46,486 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:46,488 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:46,488 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:46,488 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:46,489 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:46,489 - INFO: Starting regression paradigm
2025-07-11 14:44:46,489 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:46,489 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:46,681 - INFO: Fold 1 Training C-index: 0.5358
2025-07-11 14:44:46,851 - INFO: Fold 3 Training C-index: 0.5224
2025-07-11 14:44:46,954 - INFO: Fold 1 Test C-index: 0.5009
2025-07-11 14:44:46,954 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:46,967 - INFO: Fold 1 Validation C-index: 0.5006
2025-07-11 14:44:46,968 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:46,977 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:46,978 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:46,978 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:46,978 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:46,978 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:46,978 - INFO: Starting regression paradigm
2025-07-11 14:44:46,978 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:46,978 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,104 - INFO: Fold 3 Test C-index: 0.5303
2025-07-11 14:44:47,105 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:47,116 - INFO: Fold 3 Validation C-index: 0.4991
2025-07-11 14:44:47,117 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:47,126 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:47,127 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:47,127 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:47,127 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,127 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,127 - INFO: Starting regression paradigm
2025-07-11 14:44:47,127 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:47,127 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,329 - INFO: Fold 2 Training C-index: 0.5345
2025-07-11 14:44:47,437 - INFO: Fold 4 Training C-index: 0.5266
2025-07-11 14:44:47,566 - INFO: Fold 2 Test C-index: 0.5243
2025-07-11 14:44:47,567 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:47,579 - INFO: Fold 2 Validation C-index: 0.4976
2025-07-11 14:44:47,579 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:47,587 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:47,588 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:47,588 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:47,588 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,588 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,589 - INFO: Starting regression paradigm
2025-07-11 14:44:47,589 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:47,589 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,653 - INFO: Fold 4 Test C-index: 0.5121
2025-07-11 14:44:47,653 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:47,666 - INFO: Fold 4 Validation C-index: 0.5054
2025-07-11 14:44:47,666 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:47,674 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:47,675 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:47,675 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:47,675 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,675 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,675 - INFO: Starting regression paradigm
2025-07-11 14:44:47,675 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:47,675 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,821 - INFO: Fold 3 Training C-index: 0.5341
2025-07-11 14:44:47,885 - INFO: Fold 5 Training C-index: 0.5209
2025-07-11 14:44:48,039 - INFO: Fold 3 Test C-index: 0.5302
2025-07-11 14:44:48,039 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:48,050 - INFO: Fold 3 Validation C-index: 0.4930
2025-07-11 14:44:48,050 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:48,057 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,058 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,058 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,058 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,058 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,058 - INFO: Starting regression paradigm
2025-07-11 14:44:48,058 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:48,058 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,077 - INFO: Fold 5 Test C-index: 0.5353
2025-07-11 14:44:48,077 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:48,087 - INFO: Fold 5 Validation C-index: 0.5036
2025-07-11 14:44:48,087 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:48,087 - INFO: C-indices per fold: ['0.5007', '0.5048', '0.4991', '0.5054', '0.5036']
2025-07-11 14:44:48,087 - INFO: Mean C-index: 0.5027
2025-07-11 14:44:48,087 - INFO: Std Dev of C-index: 0.0024
2025-07-11 14:44:48,087 - INFO: Worst C-index: 0.4991
2025-07-11 14:44:48,088 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:48,088 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-11 14:44:48,156 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:48,159 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:48,164 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:48,192 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,196 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,196 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,196 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,196 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,196 - INFO: Starting regression paradigm
2025-07-11 14:44:48,196 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:48,196 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,258 - INFO: Fold 4 Training C-index: 0.5292
2025-07-11 14:44:48,458 - INFO: Fold 4 Test C-index: 0.5364
2025-07-11 14:44:48,458 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:48,470 - INFO: Fold 4 Validation C-index: 0.5033
2025-07-11 14:44:48,470 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:48,477 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,478 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,478 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,478 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,479 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,479 - INFO: Starting regression paradigm
2025-07-11 14:44:48,479 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:48,479 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,715 - INFO: Fold 5 Training C-index: 0.5282
2025-07-11 14:44:49,011 - INFO: Fold 5 Test C-index: 0.5383
2025-07-11 14:44:49,011 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:49,028 - INFO: Fold 5 Validation C-index: 0.4975
2025-07-11 14:44:49,028 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:49,029 - INFO: C-indices per fold: ['0.5006', '0.4976', '0.4930', '0.5033', '0.4975']
2025-07-11 14:44:49,029 - INFO: Mean C-index: 0.4984
2025-07-11 14:44:49,029 - INFO: Std Dev of C-index: 0.0034
2025-07-11 14:44:49,029 - INFO: Worst C-index: 0.4930
2025-07-11 14:44:49,030 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:49,030 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-11 14:44:49,039 - INFO: Fold 1 Training C-index: 0.5394
2025-07-11 14:44:49,102 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:49,105 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:49,111 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:49,141 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:49,147 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:49,147 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:49,147 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,147 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,147 - INFO: Starting regression paradigm
2025-07-11 14:44:49,148 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:49,148 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:49,261 - INFO: Fold 1 Test C-index: 0.5133
2025-07-11 14:44:49,262 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:49,272 - INFO: Fold 1 Validation C-index: 0.5099
2025-07-11 14:44:49,273 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:49,279 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:49,280 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:49,280 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:49,280 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,280 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,280 - INFO: Starting regression paradigm
2025-07-11 14:44:49,280 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:49,280 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:49,468 - INFO: Fold 2 Training C-index: 0.5303
2025-07-11 14:44:49,675 - INFO: Fold 2 Test C-index: 0.5285
2025-07-11 14:44:49,675 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:49,686 - INFO: Fold 2 Validation C-index: 0.5090
2025-07-11 14:44:49,686 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:49,693 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:49,693 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:49,694 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:49,694 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,694 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,694 - INFO: Starting regression paradigm
2025-07-11 14:44:49,694 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:49,694 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:49,959 - INFO: Fold 1 Training C-index: 0.5394
2025-07-11 14:44:49,968 - INFO: Fold 3 Training C-index: 0.5243
2025-07-11 14:44:50,248 - INFO: Fold 3 Test C-index: 0.5500
2025-07-11 14:44:50,248 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:50,258 - INFO: Fold 1 Test C-index: 0.5238
2025-07-11 14:44:50,259 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:50,259 - INFO: Fold 3 Validation C-index: 0.5187
2025-07-11 14:44:50,259 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:50,266 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,267 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,267 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,267 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,268 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,268 - INFO: Starting regression paradigm
2025-07-11 14:44:50,268 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:50,268 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,271 - INFO: Fold 1 Validation C-index: 0.5138
2025-07-11 14:44:50,271 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:50,279 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,280 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,280 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,280 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,280 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,280 - INFO: Starting regression paradigm
2025-07-11 14:44:50,280 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:50,280 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,472 - INFO: Fold 4 Training C-index: 0.5357
2025-07-11 14:44:50,494 - INFO: Fold 2 Training C-index: 0.5343
2025-07-11 14:44:50,743 - INFO: Fold 4 Test C-index: 0.5055
2025-07-11 14:44:50,743 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:50,753 - INFO: Fold 4 Validation C-index: 0.5128
2025-07-11 14:44:50,753 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:50,760 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,761 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,761 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,761 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,761 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,761 - INFO: Starting regression paradigm
2025-07-11 14:44:50,761 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:50,761 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,770 - INFO: Fold 2 Test C-index: 0.5176
2025-07-11 14:44:50,770 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:50,781 - INFO: Fold 2 Validation C-index: 0.5181
2025-07-11 14:44:50,781 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:50,788 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,789 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,789 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,789 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,789 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,790 - INFO: Starting regression paradigm
2025-07-11 14:44:50,790 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:50,790 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,952 - INFO: Fold 5 Training C-index: 0.5330
2025-07-11 14:44:50,991 - INFO: Fold 3 Training C-index: 0.5266
2025-07-11 14:44:51,138 - INFO: Fold 5 Test C-index: 0.5055
2025-07-11 14:44:51,138 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:51,147 - INFO: Fold 5 Validation C-index: 0.5070
2025-07-11 14:44:51,147 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:51,148 - INFO: C-indices per fold: ['0.5099', '0.5090', '0.5187', '0.5128', '0.5070']
2025-07-11 14:44:51,148 - INFO: Mean C-index: 0.5115
2025-07-11 14:44:51,148 - INFO: Std Dev of C-index: 0.0041
2025-07-11 14:44:51,148 - INFO: Worst C-index: 0.5070
2025-07-11 14:44:51,149 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:51,149 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-11 14:44:51,187 - INFO: Fold 3 Test C-index: 0.5385
2025-07-11 14:44:51,187 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:51,196 - INFO: Fold 3 Validation C-index: 0.5270
2025-07-11 14:44:51,197 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:51,204 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,204 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:51,205 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,205 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,205 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,205 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,205 - INFO: Starting regression paradigm
2025-07-11 14:44:51,205 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:51,205 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,207 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:51,212 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:51,236 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,240 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,241 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,241 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,241 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,241 - INFO: Starting regression paradigm
2025-07-11 14:44:51,241 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:51,241 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,422 - INFO: Fold 4 Training C-index: 0.5407
2025-07-11 14:44:51,639 - INFO: Fold 4 Test C-index: 0.5036
2025-07-11 14:44:51,639 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:51,650 - INFO: Fold 4 Validation C-index: 0.5203
2025-07-11 14:44:51,650 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:51,658 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,659 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,659 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,659 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,659 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,659 - INFO: Starting regression paradigm
2025-07-11 14:44:51,659 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:51,659 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,855 - INFO: Fold 5 Training C-index: 0.5336
2025-07-11 14:44:52,058 - INFO: Fold 1 Training C-index: 0.5438
2025-07-11 14:44:52,138 - INFO: Fold 5 Test C-index: 0.5104
2025-07-11 14:44:52,138 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:52,150 - INFO: Fold 5 Validation C-index: 0.5139
2025-07-11 14:44:52,150 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:52,150 - INFO: C-indices per fold: ['0.5138', '0.5181', '0.5270', '0.5203', '0.5139']
2025-07-11 14:44:52,150 - INFO: Mean C-index: 0.5186
2025-07-11 14:44:52,150 - INFO: Std Dev of C-index: 0.0049
2025-07-11 14:44:52,150 - INFO: Worst C-index: 0.5138
2025-07-11 14:44:52,151 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:52,151 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-11 14:44:52,213 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:52,216 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:52,221 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:52,247 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:52,251 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:52,251 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:52,252 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,252 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,252 - INFO: Starting regression paradigm
2025-07-11 14:44:52,252 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:52,252 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:52,261 - INFO: Fold 1 Test C-index: 0.5182
2025-07-11 14:44:52,261 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:52,272 - INFO: Fold 1 Validation C-index: 0.5147
2025-07-11 14:44:52,273 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:52,279 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:52,280 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:52,280 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:52,280 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,280 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,280 - INFO: Starting regression paradigm
2025-07-11 14:44:52,280 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:52,280 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:52,485 - INFO: Fold 2 Training C-index: 0.5236
2025-07-11 14:44:52,690 - INFO: Fold 2 Test C-index: 0.5225
2025-07-11 14:44:52,690 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:52,701 - INFO: Fold 2 Validation C-index: 0.5139
2025-07-11 14:44:52,701 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:52,708 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:52,708 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:52,709 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:52,709 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,709 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,709 - INFO: Starting regression paradigm
2025-07-11 14:44:52,709 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:52,709 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:52,913 - INFO: Fold 3 Training C-index: 0.5248
2025-07-11 14:44:53,204 - INFO: Fold 1 Training C-index: 0.5438
2025-07-11 14:44:53,325 - INFO: Fold 3 Test C-index: 0.5458
2025-07-11 14:44:53,326 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:53,337 - INFO: Fold 3 Validation C-index: 0.5134
2025-07-11 14:44:53,337 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:53,344 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:53,345 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:53,345 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:53,345 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,345 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,346 - INFO: Starting regression paradigm
2025-07-11 14:44:53,346 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:53,346 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:53,425 - INFO: Fold 1 Test C-index: 0.5297
2025-07-11 14:44:53,426 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:53,436 - INFO: Fold 1 Validation C-index: 0.5091
2025-07-11 14:44:53,437 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:53,443 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:53,444 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:53,444 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:53,444 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,444 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,444 - INFO: Starting regression paradigm
2025-07-11 14:44:53,444 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:53,444 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:53,554 - INFO: Fold 4 Training C-index: 0.5415
2025-07-11 14:44:53,658 - INFO: Fold 2 Training C-index: 0.5334
2025-07-11 14:44:53,753 - INFO: Fold 4 Test C-index: 0.4759
2025-07-11 14:44:53,754 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:53,764 - INFO: Fold 4 Validation C-index: 0.5212
2025-07-11 14:44:53,764 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:53,770 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:53,771 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:53,771 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:53,771 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,771 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,771 - INFO: Starting regression paradigm
2025-07-11 14:44:53,771 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:53,771 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:53,934 - INFO: Fold 2 Test C-index: 0.5299
2025-07-11 14:44:53,934 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:53,944 - INFO: Fold 2 Validation C-index: 0.5097
2025-07-11 14:44:53,944 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:53,952 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:53,953 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:53,953 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:53,953 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,953 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,953 - INFO: Starting regression paradigm
2025-07-11 14:44:53,953 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:53,953 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,015 - INFO: Fold 5 Training C-index: 0.5292
2025-07-11 14:44:54,151 - INFO: Fold 3 Training C-index: 0.5333
2025-07-11 14:44:54,208 - INFO: Fold 5 Test C-index: 0.5452
2025-07-11 14:44:54,208 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:54,220 - INFO: Fold 5 Validation C-index: 0.5124
2025-07-11 14:44:54,220 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:54,220 - INFO: C-indices per fold: ['0.5147', '0.5139', '0.5134', '0.5212', '0.5124']
2025-07-11 14:44:54,220 - INFO: Mean C-index: 0.5151
2025-07-11 14:44:54,220 - INFO: Std Dev of C-index: 0.0031
2025-07-11 14:44:54,220 - INFO: Worst C-index: 0.5124
2025-07-11 14:44:54,221 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:54,221 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-11 14:44:54,290 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:54,293 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:54,298 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:54,324 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,329 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,329 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,329 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,329 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,329 - INFO: Starting regression paradigm
2025-07-11 14:44:54,329 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:54,329 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,365 - INFO: Fold 3 Test C-index: 0.5471
2025-07-11 14:44:54,365 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:54,375 - INFO: Fold 3 Validation C-index: 0.5076
2025-07-11 14:44:54,375 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:54,382 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,383 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,383 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,383 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,383 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,383 - INFO: Starting regression paradigm
2025-07-11 14:44:54,383 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:54,383 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,592 - INFO: Fold 4 Training C-index: 0.5521
2025-07-11 14:44:54,798 - INFO: Fold 4 Test C-index: 0.4713
2025-07-11 14:44:54,798 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:54,808 - INFO: Fold 4 Validation C-index: 0.5143
2025-07-11 14:44:54,808 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:54,814 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,815 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,815 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,815 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,815 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,815 - INFO: Starting regression paradigm
2025-07-11 14:44:54,816 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:54,816 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,068 - INFO: Fold 1 Training C-index: 0.5273
2025-07-11 14:44:55,073 - INFO: Fold 5 Training C-index: 0.5319
2025-07-11 14:44:55,257 - INFO: Fold 1 Test C-index: 0.5355
2025-07-11 14:44:55,257 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:55,266 - INFO: Fold 5 Test C-index: 0.5579
2025-07-11 14:44:55,266 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:55,268 - INFO: Fold 1 Validation C-index: 0.4935
2025-07-11 14:44:55,268 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:55,274 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:55,275 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:55,275 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:55,275 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,275 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,275 - INFO: Starting regression paradigm
2025-07-11 14:44:55,275 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:55,275 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,276 - INFO: Fold 5 Validation C-index: 0.5057
2025-07-11 14:44:55,276 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:55,276 - INFO: C-indices per fold: ['0.5091', '0.5097', '0.5076', '0.5143', '0.5057']
2025-07-11 14:44:55,276 - INFO: Mean C-index: 0.5093
2025-07-11 14:44:55,276 - INFO: Std Dev of C-index: 0.0029
2025-07-11 14:44:55,276 - INFO: Worst C-index: 0.5057
2025-07-11 14:44:55,277 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:55,278 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-11 14:44:55,336 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:55,339 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:55,345 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:55,371 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:55,376 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:55,377 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:55,377 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,377 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,377 - INFO: Starting regression paradigm
2025-07-11 14:44:55,377 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:55,377 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,474 - INFO: Fold 2 Training C-index: 0.5316
2025-07-11 14:44:55,684 - INFO: Fold 2 Test C-index: 0.5232
2025-07-11 14:44:55,684 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:55,694 - INFO: Fold 2 Validation C-index: 0.4870
2025-07-11 14:44:55,694 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:55,701 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:55,701 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:55,702 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:55,702 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,702 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,702 - INFO: Starting regression paradigm
2025-07-11 14:44:55,702 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:55,702 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,965 - INFO: Fold 3 Training C-index: 0.5255
2025-07-11 14:44:56,220 - INFO: Fold 3 Test C-index: 0.5351
2025-07-11 14:44:56,220 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:56,229 - INFO: Fold 1 Training C-index: 0.5273
2025-07-11 14:44:56,231 - INFO: Fold 3 Validation C-index: 0.4938
2025-07-11 14:44:56,231 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:56,238 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,239 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,239 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,239 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,239 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,239 - INFO: Starting regression paradigm
2025-07-11 14:44:56,239 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:56,239 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:56,437 - INFO: Fold 1 Test C-index: 0.5209
2025-07-11 14:44:56,438 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:56,440 - INFO: Fold 4 Training C-index: 0.5296
2025-07-11 14:44:56,449 - INFO: Fold 1 Validation C-index: 0.5142
2025-07-11 14:44:56,449 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:56,457 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,458 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,458 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,458 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,459 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,459 - INFO: Starting regression paradigm
2025-07-11 14:44:56,459 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:56,459 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:56,649 - INFO: Fold 4 Test C-index: 0.5281
2025-07-11 14:44:56,649 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:56,661 - INFO: Fold 4 Validation C-index: 0.4901
2025-07-11 14:44:56,661 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:56,668 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,669 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,669 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,669 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,669 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,669 - INFO: Starting regression paradigm
2025-07-11 14:44:56,669 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:56,669 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:56,672 - INFO: Fold 2 Training C-index: 0.5330
2025-07-11 14:44:56,857 - INFO: Fold 5 Training C-index: 0.5351
2025-07-11 14:44:56,872 - INFO: Fold 2 Test C-index: 0.5377
2025-07-11 14:44:56,872 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:56,882 - INFO: Fold 2 Validation C-index: 0.5012
2025-07-11 14:44:56,882 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:56,889 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,890 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,890 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,890 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,890 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,890 - INFO: Starting regression paradigm
2025-07-11 14:44:56,890 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:56,891 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,134 - INFO: Fold 5 Test C-index: 0.5018
2025-07-11 14:44:57,134 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:57,143 - INFO: Fold 5 Validation C-index: 0.4881
2025-07-11 14:44:57,143 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:57,143 - INFO: C-indices per fold: ['0.4935', '0.4870', '0.4938', '0.4901', '0.4881']
2025-07-11 14:44:57,143 - INFO: Mean C-index: 0.4905
2025-07-11 14:44:57,144 - INFO: Std Dev of C-index: 0.0028
2025-07-11 14:44:57,144 - INFO: Worst C-index: 0.4870
2025-07-11 14:44:57,145 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:57,145 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-11 14:44:57,177 - INFO: Fold 3 Training C-index: 0.5293
2025-07-11 14:44:57,212 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:57,216 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:57,222 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:57,251 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,255 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,255 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,255 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,256 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,256 - INFO: Starting regression paradigm
2025-07-11 14:44:57,256 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:57,256 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,389 - INFO: Fold 3 Test C-index: 0.5376
2025-07-11 14:44:57,390 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:57,401 - INFO: Fold 3 Validation C-index: 0.5099
2025-07-11 14:44:57,401 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:57,411 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,411 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,412 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,412 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,412 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,412 - INFO: Starting regression paradigm
2025-07-11 14:44:57,412 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:57,412 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,630 - INFO: Fold 4 Training C-index: 0.5347
2025-07-11 14:44:57,842 - INFO: Fold 4 Test C-index: 0.5277
2025-07-11 14:44:57,842 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:57,855 - INFO: Fold 4 Validation C-index: 0.5067
2025-07-11 14:44:57,855 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:57,862 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,863 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,863 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,863 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,863 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,863 - INFO: Starting regression paradigm
2025-07-11 14:44:57,863 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:57,863 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:58,065 - INFO: Fold 1 Training C-index: 0.5391
2025-07-11 14:44:58,141 - INFO: Fold 5 Training C-index: 0.5395
2025-07-11 14:44:58,269 - INFO: Fold 1 Test C-index: 0.5282
2025-07-11 14:44:58,270 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:58,282 - INFO: Fold 1 Validation C-index: 0.5093
2025-07-11 14:44:58,282 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:58,291 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:58,292 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:58,292 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:58,293 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,293 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,293 - INFO: Starting regression paradigm
2025-07-11 14:44:58,293 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:58,293 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:58,369 - INFO: Fold 5 Test C-index: 0.5019
2025-07-11 14:44:58,369 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:58,381 - INFO: Fold 5 Validation C-index: 0.5071
2025-07-11 14:44:58,381 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:58,381 - INFO: C-indices per fold: ['0.5142', '0.5012', '0.5099', '0.5067', '0.5071']
2025-07-11 14:44:58,381 - INFO: Mean C-index: 0.5078
2025-07-11 14:44:58,381 - INFO: Std Dev of C-index: 0.0043
2025-07-11 14:44:58,381 - INFO: Worst C-index: 0.5012
2025-07-11 14:44:58,382 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:58,382 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-11 14:44:58,450 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:58,454 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:58,459 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:58,490 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:58,495 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:58,495 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:58,495 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,495 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,495 - INFO: Starting regression paradigm
2025-07-11 14:44:58,495 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:58,495 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:58,516 - INFO: Fold 2 Training C-index: 0.5246
2025-07-11 14:44:58,715 - INFO: Fold 2 Test C-index: 0.5249
2025-07-11 14:44:58,715 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:58,726 - INFO: Fold 2 Validation C-index: 0.5142
2025-07-11 14:44:58,727 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:58,733 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:58,734 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:58,734 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:58,734 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,734 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,734 - INFO: Starting regression paradigm
2025-07-11 14:44:58,734 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:58,734 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,069 - INFO: Fold 3 Training C-index: 0.5336
2025-07-11 14:44:59,315 - INFO: Fold 3 Test C-index: 0.5123
2025-07-11 14:44:59,315 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:59,347 - INFO: Fold 3 Validation C-index: 0.5056
2025-07-11 14:44:59,347 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:59,355 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,356 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,356 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,356 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,356 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,357 - INFO: Starting regression paradigm
2025-07-11 14:44:59,357 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:59,357 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,370 - INFO: Fold 1 Training C-index: 0.5391
2025-07-11 14:44:59,571 - INFO: Fold 4 Training C-index: 0.5232
2025-07-11 14:44:59,605 - INFO: Fold 1 Test C-index: 0.5261
2025-07-11 14:44:59,606 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:59,616 - INFO: Fold 1 Validation C-index: 0.5108
2025-07-11 14:44:59,617 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:59,624 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,625 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,625 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,625 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,625 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,625 - INFO: Starting regression paradigm
2025-07-11 14:44:59,626 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:59,626 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,771 - INFO: Fold 4 Test C-index: 0.5473
2025-07-11 14:44:59,772 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:59,782 - INFO: Fold 4 Validation C-index: 0.5160
2025-07-11 14:44:59,782 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:59,790 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,790 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,790 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,791 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,791 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,791 - INFO: Starting regression paradigm
2025-07-11 14:44:59,791 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:59,791 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,840 - INFO: Fold 2 Training C-index: 0.5293
2025-07-11 14:45:00,004 - INFO: Fold 5 Training C-index: 0.5360
2025-07-11 14:45:00,072 - INFO: Fold 2 Test C-index: 0.5174
2025-07-11 14:45:00,072 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:00,088 - INFO: Fold 2 Validation C-index: 0.5068
2025-07-11 14:45:00,089 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:00,098 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:00,099 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:00,099 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:00,099 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:00,099 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:00,099 - INFO: Starting regression paradigm
2025-07-11 14:45:00,099 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:00,099 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:00,250 - INFO: Fold 5 Test C-index: 0.4999
2025-07-11 14:45:00,250 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:00,282 - INFO: Fold 5 Validation C-index: 0.5123
2025-07-11 14:45:00,285 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:00,285 - INFO: C-indices per fold: ['0.5093', '0.5142', '0.5056', '0.5160', '0.5123']
2025-07-11 14:45:00,285 - INFO: Mean C-index: 0.5115
2025-07-11 14:45:00,285 - INFO: Std Dev of C-index: 0.0037
2025-07-11 14:45:00,285 - INFO: Worst C-index: 0.5056
2025-07-11 14:45:00,287 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:00,288 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-11 14:45:00,438 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:00,444 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:00,456 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:00,521 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:00,532 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:00,532 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:00,532 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:00,533 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:00,533 - INFO: Starting regression paradigm
2025-07-11 14:45:00,533 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:00,533 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:00,565 - INFO: Fold 3 Training C-index: 0.5327
2025-07-11 14:45:01,027 - INFO: Fold 3 Test C-index: 0.5169
2025-07-11 14:45:01,027 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:01,042 - INFO: Fold 3 Validation C-index: 0.5014
2025-07-11 14:45:01,043 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:01,053 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:01,054 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:01,054 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:01,054 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,055 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,055 - INFO: Starting regression paradigm
2025-07-11 14:45:01,055 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:01,055 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:01,415 - INFO: Fold 4 Training C-index: 0.5244
2025-07-11 14:45:01,745 - INFO: Fold 4 Test C-index: 0.5476
2025-07-11 14:45:01,745 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:01,771 - INFO: Fold 4 Validation C-index: 0.5115
2025-07-11 14:45:01,771 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:01,792 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:01,794 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:01,794 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:01,795 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,795 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,795 - INFO: Starting regression paradigm
2025-07-11 14:45:01,795 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:01,796 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:01,845 - INFO: Fold 1 Training C-index: 0.5363
2025-07-11 14:45:02,052 - INFO: Fold 5 Training C-index: 0.5391
2025-07-11 14:45:02,063 - INFO: Fold 1 Test C-index: 0.5154
2025-07-11 14:45:02,063 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:02,074 - INFO: Fold 1 Validation C-index: 0.5009
2025-07-11 14:45:02,074 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:02,081 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:02,081 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:02,082 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:02,082 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,082 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,082 - INFO: Starting regression paradigm
2025-07-11 14:45:02,082 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:02,082 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:02,283 - INFO: Fold 5 Test C-index: 0.4946
2025-07-11 14:45:02,284 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:02,297 - INFO: Fold 5 Validation C-index: 0.5083
2025-07-11 14:45:02,297 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:02,297 - INFO: C-indices per fold: ['0.5108', '0.5068', '0.5014', '0.5115', '0.5083']
2025-07-11 14:45:02,297 - INFO: Mean C-index: 0.5078
2025-07-11 14:45:02,298 - INFO: Std Dev of C-index: 0.0036
2025-07-11 14:45:02,298 - INFO: Worst C-index: 0.5014
2025-07-11 14:45:02,299 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:02,299 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-11 14:45:02,309 - INFO: Fold 2 Training C-index: 0.5174
2025-07-11 14:45:02,386 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:02,392 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:02,404 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:02,462 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:02,466 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:02,466 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:02,466 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,466 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,467 - INFO: Starting regression paradigm
2025-07-11 14:45:02,467 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:02,467 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:02,576 - INFO: Fold 2 Test C-index: 0.5377
2025-07-11 14:45:02,576 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:02,588 - INFO: Fold 2 Validation C-index: 0.5033
2025-07-11 14:45:02,588 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:02,595 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:02,595 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:02,595 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:02,596 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,596 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,596 - INFO: Starting regression paradigm
2025-07-11 14:45:02,596 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:02,596 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:02,821 - INFO: Fold 3 Training C-index: 0.5246
2025-07-11 14:45:03,047 - INFO: Fold 3 Test C-index: 0.5242
2025-07-11 14:45:03,048 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:03,061 - INFO: Fold 3 Validation C-index: 0.5047
2025-07-11 14:45:03,061 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:03,069 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,070 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,070 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,070 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,070 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,070 - INFO: Starting regression paradigm
2025-07-11 14:45:03,070 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:03,070 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,320 - INFO: Fold 1 Training C-index: 0.5363
2025-07-11 14:45:03,364 - INFO: Fold 4 Training C-index: 0.5339
2025-07-11 14:45:03,526 - INFO: Fold 1 Test C-index: 0.5129
2025-07-11 14:45:03,526 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:03,538 - INFO: Fold 1 Validation C-index: 0.5099
2025-07-11 14:45:03,538 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:03,545 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,546 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,546 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,546 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,546 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,546 - INFO: Starting regression paradigm
2025-07-11 14:45:03,547 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:03,547 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,576 - INFO: Fold 4 Test C-index: 0.5020
2025-07-11 14:45:03,576 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:03,588 - INFO: Fold 4 Validation C-index: 0.4972
2025-07-11 14:45:03,588 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:03,595 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,596 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,596 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,596 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,596 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,596 - INFO: Starting regression paradigm
2025-07-11 14:45:03,596 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:03,596 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,757 - INFO: Fold 2 Training C-index: 0.5253
2025-07-11 14:45:03,804 - INFO: Fold 5 Training C-index: 0.5270
2025-07-11 14:45:03,976 - INFO: Fold 2 Test C-index: 0.5307
2025-07-11 14:45:03,976 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:03,987 - INFO: Fold 2 Validation C-index: 0.5083
2025-07-11 14:45:03,987 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:03,994 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,994 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,994 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,995 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,995 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,995 - INFO: Starting regression paradigm
2025-07-11 14:45:03,995 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:03,995 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:04,009 - INFO: Fold 5 Test C-index: 0.5154
2025-07-11 14:45:04,010 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:04,021 - INFO: Fold 5 Validation C-index: 0.5023
2025-07-11 14:45:04,021 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:04,021 - INFO: C-indices per fold: ['0.5009', '0.5033', '0.5047', '0.4972', '0.5023']
2025-07-11 14:45:04,021 - INFO: Mean C-index: 0.5017
2025-07-11 14:45:04,021 - INFO: Std Dev of C-index: 0.0026
2025-07-11 14:45:04,021 - INFO: Worst C-index: 0.4972
2025-07-11 14:45:04,022 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:04,022 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-11 14:45:04,096 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:04,100 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:04,105 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:04,134 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:04,139 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:04,139 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:04,139 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,139 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,139 - INFO: Starting regression paradigm
2025-07-11 14:45:04,139 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:04,139 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:04,214 - INFO: Fold 3 Training C-index: 0.5293
2025-07-11 14:45:04,508 - INFO: Fold 3 Test C-index: 0.5302
2025-07-11 14:45:04,508 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:04,520 - INFO: Fold 3 Validation C-index: 0.5114
2025-07-11 14:45:04,520 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:04,528 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:04,529 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:04,529 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:04,529 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,530 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,530 - INFO: Starting regression paradigm
2025-07-11 14:45:04,530 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:04,530 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:04,754 - INFO: Fold 4 Training C-index: 0.5371
2025-07-11 14:45:05,029 - INFO: Fold 4 Test C-index: 0.5157
2025-07-11 14:45:05,029 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:05,031 - INFO: Fold 1 Training C-index: 0.5346
2025-07-11 14:45:05,041 - INFO: Fold 4 Validation C-index: 0.5062
2025-07-11 14:45:05,042 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:05,052 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,053 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,053 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,053 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,053 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,053 - INFO: Starting regression paradigm
2025-07-11 14:45:05,053 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:05,053 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,225 - INFO: Fold 1 Test C-index: 0.5365
2025-07-11 14:45:05,225 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:05,235 - INFO: Fold 1 Validation C-index: 0.4987
2025-07-11 14:45:05,235 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:05,241 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,242 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,242 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,242 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,243 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,243 - INFO: Starting regression paradigm
2025-07-11 14:45:05,243 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:05,243 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,256 - INFO: Fold 5 Training C-index: 0.5321
2025-07-11 14:45:05,426 - INFO: Fold 2 Training C-index: 0.5208
2025-07-11 14:45:05,482 - INFO: Fold 5 Test C-index: 0.5249
2025-07-11 14:45:05,482 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:05,510 - INFO: Fold 5 Validation C-index: 0.5054
2025-07-11 14:45:05,511 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:05,511 - INFO: C-indices per fold: ['0.5099', '0.5083', '0.5114', '0.5062', '0.5054']
2025-07-11 14:45:05,511 - INFO: Mean C-index: 0.5082
2025-07-11 14:45:05,511 - INFO: Std Dev of C-index: 0.0022
2025-07-11 14:45:05,512 - INFO: Worst C-index: 0.5054
2025-07-11 14:45:05,514 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:05,515 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-11 14:45:05,583 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:05,587 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:05,593 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:05,622 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,626 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,627 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,627 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,627 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,627 - INFO: Starting regression paradigm
2025-07-11 14:45:05,627 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:05,627 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,682 - INFO: Fold 2 Test C-index: 0.5228
2025-07-11 14:45:05,682 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:05,692 - INFO: Fold 2 Validation C-index: 0.4995
2025-07-11 14:45:05,693 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:05,700 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,701 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,701 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,701 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,701 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,701 - INFO: Starting regression paradigm
2025-07-11 14:45:05,702 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:05,702 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,895 - INFO: Fold 3 Training C-index: 0.5216
2025-07-11 14:45:06,090 - INFO: Fold 3 Test C-index: 0.5204
2025-07-11 14:45:06,090 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:06,100 - INFO: Fold 3 Validation C-index: 0.4991
2025-07-11 14:45:06,100 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:06,106 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,107 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,107 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,107 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,107 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,107 - INFO: Starting regression paradigm
2025-07-11 14:45:06,107 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:06,107 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,303 - INFO: Fold 4 Training C-index: 0.5283
2025-07-11 14:45:06,396 - INFO: Fold 1 Training C-index: 0.5346
2025-07-11 14:45:06,565 - INFO: Fold 4 Test C-index: 0.4913
2025-07-11 14:45:06,565 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:06,577 - INFO: Fold 4 Validation C-index: 0.4993
2025-07-11 14:45:06,577 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:06,584 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,585 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,585 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,586 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,586 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,586 - INFO: Starting regression paradigm
2025-07-11 14:45:06,586 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:06,586 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,622 - INFO: Fold 1 Test C-index: 0.5473
2025-07-11 14:45:06,622 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:06,633 - INFO: Fold 1 Validation C-index: 0.5096
2025-07-11 14:45:06,633 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:06,641 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,641 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,641 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,642 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,642 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,642 - INFO: Starting regression paradigm
2025-07-11 14:45:06,642 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:06,642 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,784 - INFO: Fold 5 Training C-index: 0.5249
2025-07-11 14:45:06,836 - INFO: Fold 2 Training C-index: 0.5316
2025-07-11 14:45:06,977 - INFO: Fold 5 Test C-index: 0.5103
2025-07-11 14:45:06,978 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:06,990 - INFO: Fold 5 Validation C-index: 0.4962
2025-07-11 14:45:06,990 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:06,990 - INFO: C-indices per fold: ['0.4987', '0.4995', '0.4991', '0.4993', '0.4962']
2025-07-11 14:45:06,990 - INFO: Mean C-index: 0.4986
2025-07-11 14:45:06,990 - INFO: Std Dev of C-index: 0.0012
2025-07-11 14:45:06,990 - INFO: Worst C-index: 0.4962
2025-07-11 14:45:06,991 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:06,991 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-11 14:45:07,042 - INFO: Fold 2 Test C-index: 0.5278
2025-07-11 14:45:07,042 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:07,054 - INFO: Fold 2 Validation C-index: 0.5167
2025-07-11 14:45:07,055 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:07,055 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:07,058 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:07,063 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:07,064 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:07,064 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:07,064 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:07,064 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,064 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,064 - INFO: Starting regression paradigm
2025-07-11 14:45:07,064 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:07,064 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:07,093 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:07,097 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:07,097 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:07,097 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,097 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,097 - INFO: Starting regression paradigm
2025-07-11 14:45:07,097 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:07,097 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:07,280 - INFO: Fold 3 Training C-index: 0.5289
2025-07-11 14:45:07,510 - INFO: Fold 3 Test C-index: 0.5306
2025-07-11 14:45:07,510 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:07,523 - INFO: Fold 3 Validation C-index: 0.5183
2025-07-11 14:45:07,523 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:07,531 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:07,532 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:07,532 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:07,532 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,533 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,533 - INFO: Starting regression paradigm
2025-07-11 14:45:07,533 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:07,533 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:07,791 - INFO: Fold 4 Training C-index: 0.5372
2025-07-11 14:45:08,005 - INFO: Fold 1 Training C-index: 0.5410
2025-07-11 14:45:08,164 - INFO: Fold 4 Test C-index: 0.4998
2025-07-11 14:45:08,164 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:08,180 - INFO: Fold 4 Validation C-index: 0.5167
2025-07-11 14:45:08,180 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:08,189 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,190 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,190 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,190 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,191 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,191 - INFO: Starting regression paradigm
2025-07-11 14:45:08,191 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:08,191 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:08,285 - INFO: Fold 1 Test C-index: 0.5252
2025-07-11 14:45:08,285 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:08,297 - INFO: Fold 1 Validation C-index: 0.5409
2025-07-11 14:45:08,297 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:08,304 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,305 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,305 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,306 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,306 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,306 - INFO: Starting regression paradigm
2025-07-11 14:45:08,306 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:08,306 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:08,441 - INFO: Fold 5 Training C-index: 0.5321
2025-07-11 14:45:08,634 - INFO: Fold 2 Training C-index: 0.5212
2025-07-11 14:45:08,771 - INFO: Fold 5 Test C-index: 0.5048
2025-07-11 14:45:08,771 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:08,782 - INFO: Fold 5 Validation C-index: 0.5055
2025-07-11 14:45:08,782 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:08,782 - INFO: C-indices per fold: ['0.5096', '0.5167', '0.5183', '0.5167', '0.5055']
2025-07-11 14:45:08,782 - INFO: Mean C-index: 0.5134
2025-07-11 14:45:08,782 - INFO: Std Dev of C-index: 0.0049
2025-07-11 14:45:08,782 - INFO: Worst C-index: 0.5055
2025-07-11 14:45:08,783 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:08,783 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-11 14:45:08,828 - INFO: Fold 2 Test C-index: 0.5189
2025-07-11 14:45:08,828 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:08,838 - INFO: Fold 2 Validation C-index: 0.5424
2025-07-11 14:45:08,838 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:08,845 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,846 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,846 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,846 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,846 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,846 - INFO: Starting regression paradigm
2025-07-11 14:45:08,846 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:08,846 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:08,856 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:08,859 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:08,864 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:08,894 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,898 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,899 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,899 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,899 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,899 - INFO: Starting regression paradigm
2025-07-11 14:45:08,899 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:08,899 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:09,051 - INFO: Fold 3 Training C-index: 0.5197
2025-07-11 14:45:09,286 - INFO: Fold 3 Test C-index: 0.5171
2025-07-11 14:45:09,286 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:09,297 - INFO: Fold 3 Validation C-index: 0.5417
2025-07-11 14:45:09,298 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:09,305 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:09,305 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:09,306 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:09,306 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,306 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,306 - INFO: Starting regression paradigm
2025-07-11 14:45:09,306 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:09,306 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:09,523 - INFO: Fold 4 Training C-index: 0.5194
2025-07-11 14:45:09,749 - INFO: Fold 1 Training C-index: 0.5410
2025-07-11 14:45:09,824 - INFO: Fold 4 Test C-index: 0.5156
2025-07-11 14:45:09,824 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:09,837 - INFO: Fold 4 Validation C-index: 0.5385
2025-07-11 14:45:09,837 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:09,845 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:09,846 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:09,846 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:09,846 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,846 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,847 - INFO: Starting regression paradigm
2025-07-11 14:45:09,847 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:09,847 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:10,025 - INFO: Fold 1 Test C-index: 0.5113
2025-07-11 14:45:10,025 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:10,038 - INFO: Fold 1 Validation C-index: 0.5261
2025-07-11 14:45:10,038 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:10,047 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,048 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,048 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,048 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,049 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,049 - INFO: Starting regression paradigm
2025-07-11 14:45:10,049 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:10,049 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:10,109 - INFO: Fold 5 Training C-index: 0.5211
2025-07-11 14:45:10,263 - INFO: Fold 2 Training C-index: 0.5269
2025-07-11 14:45:10,311 - INFO: Fold 5 Test C-index: 0.5081
2025-07-11 14:45:10,311 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:10,321 - INFO: Fold 5 Validation C-index: 0.5369
2025-07-11 14:45:10,321 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:10,321 - INFO: C-indices per fold: ['0.5409', '0.5424', '0.5417', '0.5385', '0.5369']
2025-07-11 14:45:10,321 - INFO: Mean C-index: 0.5401
2025-07-11 14:45:10,321 - INFO: Std Dev of C-index: 0.0021
2025-07-11 14:45:10,321 - INFO: Worst C-index: 0.5369
2025-07-11 14:45:10,322 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:10,322 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-11 14:45:10,385 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:10,388 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:10,394 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:10,427 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,432 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,433 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,433 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,433 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,433 - INFO: Starting regression paradigm
2025-07-11 14:45:10,433 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:10,433 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:10,473 - INFO: Fold 2 Test C-index: 0.5295
2025-07-11 14:45:10,473 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:10,484 - INFO: Fold 2 Validation C-index: 0.5296
2025-07-11 14:45:10,484 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:10,491 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,492 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,492 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,492 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,492 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,492 - INFO: Starting regression paradigm
2025-07-11 14:45:10,492 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:10,492 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:10,705 - INFO: Fold 3 Training C-index: 0.5238
2025-07-11 14:45:10,932 - INFO: Fold 3 Test C-index: 0.5242
2025-07-11 14:45:10,933 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:10,945 - INFO: Fold 3 Validation C-index: 0.5342
2025-07-11 14:45:10,945 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:10,954 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,955 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,955 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,955 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,955 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,956 - INFO: Starting regression paradigm
2025-07-11 14:45:10,956 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:10,956 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:11,196 - INFO: Fold 4 Training C-index: 0.5217
2025-07-11 14:45:11,347 - INFO: Fold 1 Training C-index: 0.5256
2025-07-11 14:45:11,521 - INFO: Fold 4 Test C-index: 0.5207
2025-07-11 14:45:11,521 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:11,531 - INFO: Fold 4 Validation C-index: 0.5304
2025-07-11 14:45:11,531 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:11,538 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:11,539 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:11,539 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:11,539 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,539 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,539 - INFO: Starting regression paradigm
2025-07-11 14:45:11,539 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:11,539 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:11,564 - INFO: Fold 1 Test C-index: 0.5351
2025-07-11 14:45:11,564 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:11,579 - INFO: Fold 1 Validation C-index: 0.5287
2025-07-11 14:45:11,579 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:11,590 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:11,591 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:11,591 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:11,591 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,591 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,591 - INFO: Starting regression paradigm
2025-07-11 14:45:11,591 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:11,591 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:11,886 - INFO: Fold 2 Training C-index: 0.5174
2025-07-11 14:45:11,893 - INFO: Fold 5 Training C-index: 0.5278
2025-07-11 14:45:12,105 - INFO: Fold 2 Test C-index: 0.4945
2025-07-11 14:45:12,105 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:12,116 - INFO: Fold 2 Validation C-index: 0.5338
2025-07-11 14:45:12,116 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:12,121 - INFO: Fold 5 Test C-index: 0.5097
2025-07-11 14:45:12,121 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:12,123 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,124 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,124 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,124 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,124 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,124 - INFO: Starting regression paradigm
2025-07-11 14:45:12,124 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:12,124 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,133 - INFO: Fold 5 Validation C-index: 0.5283
2025-07-11 14:45:12,133 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:12,134 - INFO: C-indices per fold: ['0.5261', '0.5296', '0.5342', '0.5304', '0.5283']
2025-07-11 14:45:12,134 - INFO: Mean C-index: 0.5297
2025-07-11 14:45:12,134 - INFO: Std Dev of C-index: 0.0027
2025-07-11 14:45:12,134 - INFO: Worst C-index: 0.5261
2025-07-11 14:45:12,135 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:12,135 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-11 14:45:12,202 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:12,206 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:12,213 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:12,245 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,250 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,250 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,250 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,250 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,250 - INFO: Starting regression paradigm
2025-07-11 14:45:12,250 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:12,250 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,361 - INFO: Fold 3 Training C-index: 0.5162
2025-07-11 14:45:12,566 - INFO: Fold 3 Test C-index: 0.5069
2025-07-11 14:45:12,567 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:12,577 - INFO: Fold 3 Validation C-index: 0.5305
2025-07-11 14:45:12,577 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:12,585 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,586 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,586 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,587 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,587 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,587 - INFO: Starting regression paradigm
2025-07-11 14:45:12,587 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:12,587 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,890 - INFO: Fold 4 Training C-index: 0.5149
2025-07-11 14:45:13,175 - INFO: Fold 1 Training C-index: 0.5256
2025-07-11 14:45:13,234 - INFO: Fold 4 Test C-index: 0.5106
2025-07-11 14:45:13,234 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:13,247 - INFO: Fold 4 Validation C-index: 0.5271
2025-07-11 14:45:13,248 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:13,255 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:13,256 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:13,256 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:13,256 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,256 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,257 - INFO: Starting regression paradigm
2025-07-11 14:45:13,257 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:13,257 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:13,414 - INFO: Fold 1 Test C-index: 0.5345
2025-07-11 14:45:13,414 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:13,425 - INFO: Fold 1 Validation C-index: 0.5422
2025-07-11 14:45:13,425 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:13,432 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:13,433 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:13,433 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:13,433 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,433 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,433 - INFO: Starting regression paradigm
2025-07-11 14:45:13,434 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:13,434 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:13,461 - INFO: Fold 5 Training C-index: 0.5159
2025-07-11 14:45:13,632 - INFO: Fold 2 Training C-index: 0.5233
2025-07-11 14:45:13,649 - INFO: Fold 5 Test C-index: 0.4976
2025-07-11 14:45:13,649 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:13,660 - INFO: Fold 5 Validation C-index: 0.5280
2025-07-11 14:45:13,661 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:13,661 - INFO: C-indices per fold: ['0.5287', '0.5338', '0.5305', '0.5271', '0.5280']
2025-07-11 14:45:13,661 - INFO: Mean C-index: 0.5296
2025-07-11 14:45:13,661 - INFO: Std Dev of C-index: 0.0024
2025-07-11 14:45:13,661 - INFO: Worst C-index: 0.5271
2025-07-11 14:45:13,662 - INFO: ==================================================
2025-07-11 14:45:13,662 - INFO: Final Summary Over 10 Independent Run(s), validation set (hold-out set)
2025-07-11 14:45:13,662 - INFO: ==================================================
2025-07-11 14:45:13,662 - INFO: Average of 'mean_c_index' over 10 runs: 0.5111 (Std Dev: 0.0139)
2025-07-11 14:45:13,662 - INFO: Average of 'std_c_index' over 10 runs: 0.0026 (Std Dev: 0.0008)
2025-07-11 14:45:13,662 - INFO: Average of 'worst_c_index' over 10 runs: 0.5076 (Std Dev: 0.0142)
2025-07-11 14:45:13,662 - INFO: ==================================================
2025-07-11 14:45:13,662 - INFO: Final Summary Over 10 Independent Run(s), test set (in cross-validation)
2025-07-11 14:45:13,662 - INFO: ==================================================
2025-07-11 14:45:13,663 - INFO: Average of 'mean_c_index_test' over 10 runs: 0.5198 (Std Dev: 0.0048)
2025-07-11 14:45:13,663 - INFO: Average of 'std_c_index_test' over 10 runs: 0.0155 (Std Dev: 0.0054)
2025-07-11 14:45:13,663 - INFO: Average of 'worst_c_index_test' over 10 runs: 0.4956 (Std Dev: 0.0098)
2025-07-11 14:45:13,837 - INFO: Fold 2 Test C-index: 0.5024
2025-07-11 14:45:13,838 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:13,851 - INFO: Fold 2 Validation C-index: 0.5503
2025-07-11 14:45:13,851 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:13,857 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:13,858 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:13,858 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:13,858 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,859 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,859 - INFO: Starting regression paradigm
2025-07-11 14:45:13,859 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:13,859 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:14,062 - INFO: Fold 3 Training C-index: 0.5199
2025-07-11 14:45:14,280 - INFO: Fold 3 Test C-index: 0.5295
2025-07-11 14:45:14,280 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:14,290 - INFO: Fold 3 Validation C-index: 0.5448
2025-07-11 14:45:14,290 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:14,297 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:14,298 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:14,298 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:14,298 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:14,298 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:14,298 - INFO: Starting regression paradigm
2025-07-11 14:45:14,298 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:14,298 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:14,488 - INFO: Fold 4 Training C-index: 0.5256
2025-07-11 14:45:14,738 - INFO: Fold 4 Test C-index: 0.5067
2025-07-11 14:45:14,738 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:14,746 - INFO: Fold 4 Validation C-index: 0.5398
2025-07-11 14:45:14,746 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:14,753 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:14,753 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:14,753 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:14,754 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:14,754 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:14,754 - INFO: Starting regression paradigm
2025-07-11 14:45:14,754 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:14,754 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:14,916 - INFO: Fold 5 Training C-index: 0.5254
2025-07-11 14:45:15,085 - INFO: Fold 5 Test C-index: 0.5023
2025-07-11 14:45:15,085 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:15,094 - INFO: Fold 5 Validation C-index: 0.5431
2025-07-11 14:45:15,094 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:15,094 - INFO: C-indices per fold: ['0.5422', '0.5503', '0.5448', '0.5398', '0.5431']
2025-07-11 14:45:15,094 - INFO: Mean C-index: 0.5440
2025-07-11 14:45:15,094 - INFO: Std Dev of C-index: 0.0035
2025-07-11 14:45:15,094 - INFO: Worst C-index: 0.5398
2025-07-11 14:45:15,095 - INFO: ==================================================
2025-07-11 14:45:15,095 - INFO: Final Summary Over 10 Independent Run(s), validation set (hold-out set)
2025-07-11 14:45:15,095 - INFO: ==================================================
2025-07-11 14:45:15,095 - INFO: Average of 'mean_c_index' over 10 runs: 0.5142 (Std Dev: 0.0128)
2025-07-11 14:45:15,095 - INFO: Average of 'std_c_index' over 10 runs: 0.0035 (Std Dev: 0.0010)
2025-07-11 14:45:15,095 - INFO: Average of 'worst_c_index' over 10 runs: 0.5094 (Std Dev: 0.0131)
2025-07-11 14:45:15,095 - INFO: ==================================================
2025-07-11 14:45:15,095 - INFO: Final Summary Over 10 Independent Run(s), test set (in cross-validation)
2025-07-11 14:45:15,095 - INFO: ==================================================
2025-07-11 14:45:15,095 - INFO: Average of 'mean_c_index_test' over 10 runs: 0.5222 (Std Dev: 0.0037)
2025-07-11 14:45:15,095 - INFO: Average of 'std_c_index_test' over 10 runs: 0.0156 (Std Dev: 0.0066)
2025-07-11 14:45:15,096 - INFO: Average of 'worst_c_index_test' over 10 runs: 0.4977 (Std Dev: 0.0122)
