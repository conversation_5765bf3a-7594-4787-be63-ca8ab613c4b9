2025-07-11 14:44:42,105 - INFO: p: 10
2025-07-11 14:44:42,106 - INFO: n: 2000
2025-07-11 14:44:42,106 - INFO: V_ratio: 0.5
2025-07-11 14:44:42,106 - INFO: Vb_ratio: 0.1
2025-07-11 14:44:42,106 - INFO: true_func: linear
2025-07-11 14:44:42,106 - INFO: mode: S_|_V
2025-07-11 14:44:42,106 - INFO: misspe: poly
2025-07-11 14:44:42,108 - INFO: corr_s: 0.9
2025-07-11 14:44:42,109 - INFO: corr_v: 0.1
2025-07-11 14:44:42,109 - INFO: mms_strength: 1.0
2025-07-11 14:44:42,110 - INFO: spurious: nonlinear
2025-07-11 14:44:42,110 - INFO: r_train: 2.5
2025-07-11 14:44:42,110 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-11 14:44:42,110 - INFO: noise_variance: 0.3
2025-07-11 14:44:42,110 - INFO: reweighting: None
2025-07-11 14:44:42,110 - INFO: decorrelation_type: global
2025-07-11 14:44:42,110 - INFO: order: 1
2025-07-11 14:44:42,110 - INFO: iters_balance: 2500
2025-07-11 14:44:42,110 - INFO: topN: 20
2025-07-11 14:44:42,110 - INFO: backend: LogLogistic
2025-07-11 14:44:42,110 - INFO: paradigm: regr
2025-07-11 14:44:42,110 - INFO: iters_train: 5000
2025-07-11 14:44:42,110 - INFO: lam_backend: 0.03
2025-07-11 14:44:42,110 - INFO: lam_backend2: 0.03
2025-07-11 14:44:42,110 - INFO: fs_type: STG
2025-07-11 14:44:42,110 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-11 14:44:42,110 - INFO: mask_threshold: 0.2
2025-07-11 14:44:42,110 - INFO: lam_STG: 3
2025-07-11 14:44:42,110 - INFO: sigma_STG: 0.1
2025-07-11 14:44:42,111 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-11 14:44:42,111 - INFO: bv_analysis: False
2025-07-11 14:44:42,111 - INFO: seed: 2
2025-07-11 14:44:42,111 - INFO: times: 10
2025-07-11 14:44:42,111 - INFO: result_dir: results
2025-07-11 14:44:42,111 - INFO: n_splits: 5
2025-07-11 14:44:42,111 - INFO: 
2025-07-11 14:44:42,111 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:42,111 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-11 14:44:42,209 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:42,212 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:42,218 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:42,247 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:42,252 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:42,252 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:42,252 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,252 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,252 - INFO: Starting regression paradigm
2025-07-11 14:44:42,252 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:42,252 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:42,471 - INFO: p: 10
2025-07-11 14:44:42,471 - INFO: n: 2000
2025-07-11 14:44:42,471 - INFO: V_ratio: 0.5
2025-07-11 14:44:42,472 - INFO: Vb_ratio: 0.1
2025-07-11 14:44:42,472 - INFO: true_func: linear
2025-07-11 14:44:42,472 - INFO: mode: S_|_V
2025-07-11 14:44:42,472 - INFO: misspe: poly
2025-07-11 14:44:42,472 - INFO: corr_s: 0.9
2025-07-11 14:44:42,472 - INFO: corr_v: 0.1
2025-07-11 14:44:42,473 - INFO: mms_strength: 1.0
2025-07-11 14:44:42,473 - INFO: spurious: nonlinear
2025-07-11 14:44:42,473 - INFO: r_train: 2.5
2025-07-11 14:44:42,473 - INFO: r_list: [-3, -2, -1.7, -1.5, -1.3, 1.3, 1.5, 1.7, 2, 3]
2025-07-11 14:44:42,473 - INFO: noise_variance: 0.3
2025-07-11 14:44:42,473 - INFO: reweighting: None
2025-07-11 14:44:42,473 - INFO: decorrelation_type: global
2025-07-11 14:44:42,473 - INFO: order: 1
2025-07-11 14:44:42,473 - INFO: iters_balance: 2500
2025-07-11 14:44:42,473 - INFO: topN: 5
2025-07-11 14:44:42,473 - INFO: backend: LogLogistic
2025-07-11 14:44:42,473 - INFO: paradigm: regr
2025-07-11 14:44:42,473 - INFO: iters_train: 5000
2025-07-11 14:44:42,473 - INFO: lam_backend: 0.03
2025-07-11 14:44:42,473 - INFO: lam_backend2: 0.03
2025-07-11 14:44:42,473 - INFO: fs_type: STG
2025-07-11 14:44:42,473 - INFO: mask_given: [1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
2025-07-11 14:44:42,473 - INFO: mask_threshold: 0.2
2025-07-11 14:44:42,473 - INFO: lam_STG: 3
2025-07-11 14:44:42,473 - INFO: sigma_STG: 0.1
2025-07-11 14:44:42,473 - INFO: metrics: ['L1_beta_error', 'L2_beta_error']
2025-07-11 14:44:42,473 - INFO: bv_analysis: False
2025-07-11 14:44:42,473 - INFO: seed: 2
2025-07-11 14:44:42,473 - INFO: times: 10
2025-07-11 14:44:42,473 - INFO: result_dir: results
2025-07-11 14:44:42,473 - INFO: n_splits: 5
2025-07-11 14:44:42,473 - INFO: 
2025-07-11 14:44:42,474 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 0 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:42,474 - INFO: --- Starting Round 0 with Seed 2 ---
2025-07-11 14:44:42,565 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:42,569 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:42,576 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:42,611 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:42,615 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:42,616 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:42,616 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,616 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:42,616 - INFO: Starting regression paradigm
2025-07-11 14:44:42,616 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:42,616 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,124 - INFO: Fold 1 Training C-index: 0.5516
2025-07-11 14:44:43,344 - INFO: Fold 1 Test C-index: 0.4846
2025-07-11 14:44:43,344 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:43,356 - INFO: Fold 1 Validation C-index: 0.5032
2025-07-11 14:44:43,357 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:43,365 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,366 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,366 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,366 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,366 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,366 - INFO: Starting regression paradigm
2025-07-11 14:44:43,366 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:43,366 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,475 - INFO: Fold 1 Training C-index: 0.5516
2025-07-11 14:44:43,645 - INFO: Fold 2 Training C-index: 0.5285
2025-07-11 14:44:43,677 - INFO: Fold 1 Test C-index: 0.4690
2025-07-11 14:44:43,677 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:43,688 - INFO: Fold 1 Validation C-index: 0.4982
2025-07-11 14:44:43,688 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:43,695 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,695 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,695 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,696 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,696 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,696 - INFO: Starting regression paradigm
2025-07-11 14:44:43,696 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:43,696 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:43,865 - INFO: Fold 2 Test C-index: 0.5601
2025-07-11 14:44:43,865 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:43,877 - INFO: Fold 2 Validation C-index: 0.5041
2025-07-11 14:44:43,878 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:43,884 - INFO: Fold 2 Training C-index: 0.5209
2025-07-11 14:44:43,887 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:43,888 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:43,888 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:43,888 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,888 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:43,888 - INFO: Starting regression paradigm
2025-07-11 14:44:43,889 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:43,889 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,063 - INFO: Fold 2 Test C-index: 0.5195
2025-07-11 14:44:44,063 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:44,073 - INFO: Fold 2 Validation C-index: 0.4973
2025-07-11 14:44:44,073 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:44,080 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,081 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,081 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,081 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,081 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,081 - INFO: Starting regression paradigm
2025-07-11 14:44:44,081 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:44,081 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,095 - INFO: Fold 3 Training C-index: 0.5327
2025-07-11 14:44:44,266 - INFO: Fold 3 Training C-index: 0.5158
2025-07-11 14:44:44,310 - INFO: Fold 3 Test C-index: 0.5383
2025-07-11 14:44:44,311 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:44,322 - INFO: Fold 3 Validation C-index: 0.5022
2025-07-11 14:44:44,322 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:44,331 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,332 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,333 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,333 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,333 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,333 - INFO: Starting regression paradigm
2025-07-11 14:44:44,333 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:44,333 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,447 - INFO: Fold 3 Test C-index: 0.5269
2025-07-11 14:44:44,447 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:44,457 - INFO: Fold 3 Validation C-index: 0.4986
2025-07-11 14:44:44,457 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:44,463 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,464 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,464 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,464 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,464 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,464 - INFO: Starting regression paradigm
2025-07-11 14:44:44,464 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:44,464 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,551 - INFO: Fold 4 Training C-index: 0.5360
2025-07-11 14:44:44,665 - INFO: Fold 4 Training C-index: 0.5203
2025-07-11 14:44:44,796 - INFO: Fold 4 Test C-index: 0.5241
2025-07-11 14:44:44,797 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:44,808 - INFO: Fold 4 Validation C-index: 0.5070
2025-07-11 14:44:44,808 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:44,816 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,817 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,817 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,817 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,818 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,818 - INFO: Starting regression paradigm
2025-07-11 14:44:44,818 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:44,818 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:44,875 - INFO: Fold 4 Test C-index: 0.5156
2025-07-11 14:44:44,875 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:44,887 - INFO: Fold 4 Validation C-index: 0.4980
2025-07-11 14:44:44,888 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:44,893 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:44,894 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:44,894 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:44,894 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,894 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:44,895 - INFO: Starting regression paradigm
2025-07-11 14:44:44,895 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:44,895 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:45,227 - INFO: Fold 5 Training C-index: 0.5390
2025-07-11 14:44:45,270 - INFO: Fold 5 Training C-index: 0.5111
2025-07-11 14:44:45,573 - INFO: Fold 5 Test C-index: 0.5519
2025-07-11 14:44:45,574 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:45,593 - INFO: Fold 5 Validation C-index: 0.4949
2025-07-11 14:44:45,593 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:45,593 - INFO: C-indices per fold: ['0.4982', '0.4973', '0.4986', '0.4980', '0.4949']
2025-07-11 14:44:45,594 - INFO: Mean C-index: 0.4974
2025-07-11 14:44:45,594 - INFO: Std Dev of C-index: 0.0013
2025-07-11 14:44:45,594 - INFO: Worst C-index: 0.4949
2025-07-11 14:44:45,594 - INFO: Fold 5 Test C-index: 0.5291
2025-07-11 14:44:45,594 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:45,595 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:45,596 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-11 14:44:45,618 - INFO: Fold 5 Validation C-index: 0.5017
2025-07-11 14:44:45,618 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:45,618 - INFO: C-indices per fold: ['0.5032', '0.5041', '0.5022', '0.5070', '0.5017']
2025-07-11 14:44:45,618 - INFO: Mean C-index: 0.5037
2025-07-11 14:44:45,618 - INFO: Std Dev of C-index: 0.0019
2025-07-11 14:44:45,618 - INFO: Worst C-index: 0.5017
2025-07-11 14:44:45,620 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 1 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:45,620 - INFO: --- Starting Round 1 with Seed 3 ---
2025-07-11 14:44:45,723 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:45,730 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:45,743 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:45,757 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:45,763 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:45,776 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:45,819 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:45,829 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:45,829 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:45,830 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:45,830 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:45,830 - INFO: Starting regression paradigm
2025-07-11 14:44:45,830 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:45,830 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:45,845 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:45,856 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:45,856 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:45,857 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:45,857 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:45,857 - INFO: Starting regression paradigm
2025-07-11 14:44:45,857 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:45,857 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,227 - INFO: Fold 1 Training C-index: 0.5358
2025-07-11 14:44:47,312 - INFO: Fold 1 Training C-index: 0.5358
2025-07-11 14:44:47,482 - INFO: Fold 1 Test C-index: 0.4951
2025-07-11 14:44:47,483 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:47,493 - INFO: Fold 1 Validation C-index: 0.5045
2025-07-11 14:44:47,494 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:47,500 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:47,501 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:47,501 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:47,501 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,502 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,502 - INFO: Starting regression paradigm
2025-07-11 14:44:47,502 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:47,502 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,565 - INFO: Fold 1 Test C-index: 0.5105
2025-07-11 14:44:47,565 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:47,579 - INFO: Fold 1 Validation C-index: 0.4945
2025-07-11 14:44:47,579 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:47,588 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:47,589 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:47,589 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:47,589 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,589 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,589 - INFO: Starting regression paradigm
2025-07-11 14:44:47,589 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:47,589 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:47,714 - INFO: Fold 2 Training C-index: 0.5181
2025-07-11 14:44:47,830 - INFO: Fold 2 Training C-index: 0.5375
2025-07-11 14:44:47,920 - INFO: Fold 2 Test C-index: 0.5347
2025-07-11 14:44:47,920 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:47,931 - INFO: Fold 2 Validation C-index: 0.5052
2025-07-11 14:44:47,931 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:47,938 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:47,938 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:47,938 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:47,938 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,939 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:47,939 - INFO: Starting regression paradigm
2025-07-11 14:44:47,939 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:47,939 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,054 - INFO: Fold 2 Test C-index: 0.5338
2025-07-11 14:44:48,054 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:48,064 - INFO: Fold 2 Validation C-index: 0.4892
2025-07-11 14:44:48,065 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:48,072 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,072 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,073 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,073 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,073 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,073 - INFO: Starting regression paradigm
2025-07-11 14:44:48,073 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:48,073 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,122 - INFO: Fold 3 Training C-index: 0.5159
2025-07-11 14:44:48,290 - INFO: Fold 3 Training C-index: 0.5412
2025-07-11 14:44:48,308 - INFO: Fold 3 Test C-index: 0.5446
2025-07-11 14:44:48,308 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:48,318 - INFO: Fold 3 Validation C-index: 0.5006
2025-07-11 14:44:48,318 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:48,324 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,325 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,325 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,325 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,325 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,325 - INFO: Starting regression paradigm
2025-07-11 14:44:48,325 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:48,325 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,511 - INFO: Fold 3 Test C-index: 0.5290
2025-07-11 14:44:48,511 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:48,514 - INFO: Fold 4 Training C-index: 0.5266
2025-07-11 14:44:48,524 - INFO: Fold 3 Validation C-index: 0.4880
2025-07-11 14:44:48,524 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:48,533 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,534 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,534 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,535 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,535 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,535 - INFO: Starting regression paradigm
2025-07-11 14:44:48,535 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:48,535 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,726 - INFO: Fold 4 Test C-index: 0.4948
2025-07-11 14:44:48,726 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:48,738 - INFO: Fold 4 Validation C-index: 0.5056
2025-07-11 14:44:48,738 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:48,744 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:48,745 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:48,745 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:48,745 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,746 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:48,746 - INFO: Starting regression paradigm
2025-07-11 14:44:48,746 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:48,746 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:48,788 - INFO: Fold 4 Training C-index: 0.5336
2025-07-11 14:44:49,018 - INFO: Fold 5 Training C-index: 0.5194
2025-07-11 14:44:49,115 - INFO: Fold 4 Test C-index: 0.5437
2025-07-11 14:44:49,116 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:49,128 - INFO: Fold 4 Validation C-index: 0.4953
2025-07-11 14:44:49,128 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:49,136 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:49,137 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:49,138 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:49,138 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,138 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,138 - INFO: Starting regression paradigm
2025-07-11 14:44:49,138 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:49,138 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:49,236 - INFO: Fold 5 Test C-index: 0.5305
2025-07-11 14:44:49,236 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:49,246 - INFO: Fold 5 Validation C-index: 0.5023
2025-07-11 14:44:49,246 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:49,246 - INFO: C-indices per fold: ['0.5045', '0.5052', '0.5006', '0.5056', '0.5023']
2025-07-11 14:44:49,246 - INFO: Mean C-index: 0.5036
2025-07-11 14:44:49,246 - INFO: Std Dev of C-index: 0.0019
2025-07-11 14:44:49,246 - INFO: Worst C-index: 0.5006
2025-07-11 14:44:49,247 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:49,247 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-11 14:44:49,311 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:49,314 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:49,320 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:49,347 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:49,351 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:49,352 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:49,352 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,352 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,352 - INFO: Starting regression paradigm
2025-07-11 14:44:49,352 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:49,352 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:49,368 - INFO: Fold 5 Training C-index: 0.5376
2025-07-11 14:44:49,580 - INFO: Fold 5 Test C-index: 0.5389
2025-07-11 14:44:49,580 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:49,592 - INFO: Fold 5 Validation C-index: 0.4889
2025-07-11 14:44:49,593 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:49,593 - INFO: C-indices per fold: ['0.4945', '0.4892', '0.4880', '0.4953', '0.4889']
2025-07-11 14:44:49,593 - INFO: Mean C-index: 0.4912
2025-07-11 14:44:49,593 - INFO: Std Dev of C-index: 0.0031
2025-07-11 14:44:49,593 - INFO: Worst C-index: 0.4880
2025-07-11 14:44:49,594 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 2 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:49,594 - INFO: --- Starting Round 2 with Seed 4 ---
2025-07-11 14:44:49,656 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:49,659 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:49,664 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:49,693 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:49,697 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:49,697 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:49,698 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,698 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:49,698 - INFO: Starting regression paradigm
2025-07-11 14:44:49,698 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:49,698 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,230 - INFO: Fold 1 Training C-index: 0.5394
2025-07-11 14:44:50,432 - INFO: Fold 1 Test C-index: 0.4918
2025-07-11 14:44:50,432 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:50,443 - INFO: Fold 1 Validation C-index: 0.5197
2025-07-11 14:44:50,443 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:50,448 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,449 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,449 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,449 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,449 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,449 - INFO: Starting regression paradigm
2025-07-11 14:44:50,449 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:50,449 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,648 - INFO: Fold 1 Training C-index: 0.5394
2025-07-11 14:44:50,709 - INFO: Fold 2 Training C-index: 0.5144
2025-07-11 14:44:50,871 - INFO: Fold 1 Test C-index: 0.5293
2025-07-11 14:44:50,871 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:50,882 - INFO: Fold 1 Validation C-index: 0.5110
2025-07-11 14:44:50,882 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:50,890 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,891 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,891 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,891 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,891 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,891 - INFO: Starting regression paradigm
2025-07-11 14:44:50,891 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:50,891 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:50,893 - INFO: Fold 2 Test C-index: 0.5371
2025-07-11 14:44:50,893 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:50,902 - INFO: Fold 2 Validation C-index: 0.5139
2025-07-11 14:44:50,903 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:50,908 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:50,909 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:50,909 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:50,909 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,909 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:50,910 - INFO: Starting regression paradigm
2025-07-11 14:44:50,910 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:50,910 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,092 - INFO: Fold 3 Training C-index: 0.5145
2025-07-11 14:44:51,102 - INFO: Fold 2 Training C-index: 0.5369
2025-07-11 14:44:51,266 - INFO: Fold 3 Test C-index: 0.5340
2025-07-11 14:44:51,267 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:51,277 - INFO: Fold 3 Validation C-index: 0.5247
2025-07-11 14:44:51,277 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:51,283 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,284 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,284 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,284 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,284 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,285 - INFO: Starting regression paradigm
2025-07-11 14:44:51,285 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:51,285 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,314 - INFO: Fold 2 Test C-index: 0.5253
2025-07-11 14:44:51,314 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:51,326 - INFO: Fold 2 Validation C-index: 0.5138
2025-07-11 14:44:51,326 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:51,336 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,337 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,337 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,337 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,337 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,337 - INFO: Starting regression paradigm
2025-07-11 14:44:51,337 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:51,337 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,490 - INFO: Fold 4 Training C-index: 0.5173
2025-07-11 14:44:51,568 - INFO: Fold 3 Training C-index: 0.5287
2025-07-11 14:44:51,680 - INFO: Fold 4 Test C-index: 0.5211
2025-07-11 14:44:51,680 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:51,689 - INFO: Fold 4 Validation C-index: 0.5166
2025-07-11 14:44:51,689 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:51,695 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,696 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,696 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,696 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,696 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,697 - INFO: Starting regression paradigm
2025-07-11 14:44:51,697 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:51,697 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,786 - INFO: Fold 3 Test C-index: 0.5472
2025-07-11 14:44:51,786 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:51,796 - INFO: Fold 3 Validation C-index: 0.5238
2025-07-11 14:44:51,796 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:51,804 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:51,804 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:51,805 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:51,805 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,805 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:51,805 - INFO: Starting regression paradigm
2025-07-11 14:44:51,805 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:51,805 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:51,874 - INFO: Fold 5 Training C-index: 0.5250
2025-07-11 14:44:52,098 - INFO: Fold 4 Training C-index: 0.5411
2025-07-11 14:44:52,124 - INFO: Fold 5 Test C-index: 0.4927
2025-07-11 14:44:52,124 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:52,134 - INFO: Fold 5 Validation C-index: 0.5147
2025-07-11 14:44:52,134 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:52,134 - INFO: C-indices per fold: ['0.5197', '0.5139', '0.5247', '0.5166', '0.5147']
2025-07-11 14:44:52,134 - INFO: Mean C-index: 0.5179
2025-07-11 14:44:52,134 - INFO: Std Dev of C-index: 0.0039
2025-07-11 14:44:52,134 - INFO: Worst C-index: 0.5139
2025-07-11 14:44:52,135 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:52,135 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-11 14:44:52,199 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:52,202 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:52,207 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:52,234 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:52,239 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:52,239 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:52,239 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,239 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,239 - INFO: Starting regression paradigm
2025-07-11 14:44:52,239 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:52,239 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:52,323 - INFO: Fold 4 Test C-index: 0.5056
2025-07-11 14:44:52,323 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:52,335 - INFO: Fold 4 Validation C-index: 0.5152
2025-07-11 14:44:52,335 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:52,343 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:52,344 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:52,344 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:52,344 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,345 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,345 - INFO: Starting regression paradigm
2025-07-11 14:44:52,345 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:52,345 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:52,567 - INFO: Fold 5 Training C-index: 0.5356
2025-07-11 14:44:52,789 - INFO: Fold 5 Test C-index: 0.5163
2025-07-11 14:44:52,790 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:52,801 - INFO: Fold 5 Validation C-index: 0.5132
2025-07-11 14:44:52,801 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:52,802 - INFO: C-indices per fold: ['0.5110', '0.5138', '0.5238', '0.5152', '0.5132']
2025-07-11 14:44:52,802 - INFO: Mean C-index: 0.5154
2025-07-11 14:44:52,802 - INFO: Std Dev of C-index: 0.0044
2025-07-11 14:44:52,802 - INFO: Worst C-index: 0.5110
2025-07-11 14:44:52,803 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 3 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:52,803 - INFO: --- Starting Round 3 with Seed 5 ---
2025-07-11 14:44:52,869 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:52,873 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:52,879 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:52,908 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:52,912 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:52,912 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:52,912 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,912 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:52,912 - INFO: Starting regression paradigm
2025-07-11 14:44:52,912 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:52,912 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:53,204 - INFO: Fold 1 Training C-index: 0.5438
2025-07-11 14:44:53,399 - INFO: Fold 1 Test C-index: 0.4937
2025-07-11 14:44:53,399 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:53,409 - INFO: Fold 1 Validation C-index: 0.5111
2025-07-11 14:44:53,409 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:53,415 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:53,416 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:53,416 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:53,416 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,416 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,416 - INFO: Starting regression paradigm
2025-07-11 14:44:53,416 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:53,416 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:53,613 - INFO: Fold 2 Training C-index: 0.5141
2025-07-11 14:44:53,829 - INFO: Fold 2 Test C-index: 0.5312
2025-07-11 14:44:53,829 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:53,856 - INFO: Fold 2 Validation C-index: 0.5083
2025-07-11 14:44:53,856 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:53,869 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:53,871 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:53,871 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:53,872 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,872 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:53,872 - INFO: Starting regression paradigm
2025-07-11 14:44:53,872 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:53,872 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:53,909 - INFO: Fold 1 Training C-index: 0.5438
2025-07-11 14:44:54,060 - INFO: Fold 3 Training C-index: 0.5133
2025-07-11 14:44:54,118 - INFO: Fold 1 Test C-index: 0.5402
2025-07-11 14:44:54,118 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:54,130 - INFO: Fold 1 Validation C-index: 0.5123
2025-07-11 14:44:54,130 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:54,139 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,140 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,140 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,140 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,140 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,140 - INFO: Starting regression paradigm
2025-07-11 14:44:54,140 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:54,140 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,254 - INFO: Fold 3 Test C-index: 0.5500
2025-07-11 14:44:54,254 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:54,265 - INFO: Fold 3 Validation C-index: 0.5072
2025-07-11 14:44:54,265 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:54,272 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,273 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,273 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,273 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,273 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,273 - INFO: Starting regression paradigm
2025-07-11 14:44:54,273 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:54,273 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,371 - INFO: Fold 2 Training C-index: 0.5340
2025-07-11 14:44:54,459 - INFO: Fold 4 Training C-index: 0.5353
2025-07-11 14:44:54,591 - INFO: Fold 2 Test C-index: 0.5326
2025-07-11 14:44:54,591 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:54,603 - INFO: Fold 2 Validation C-index: 0.5132
2025-07-11 14:44:54,603 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:54,611 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,612 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,612 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,612 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,612 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,612 - INFO: Starting regression paradigm
2025-07-11 14:44:54,612 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:54,613 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,646 - INFO: Fold 4 Test C-index: 0.4651
2025-07-11 14:44:54,646 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:54,657 - INFO: Fold 4 Validation C-index: 0.5135
2025-07-11 14:44:54,657 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:54,662 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:54,663 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:54,663 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:54,663 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,663 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:54,663 - INFO: Starting regression paradigm
2025-07-11 14:44:54,663 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:54,663 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:54,822 - INFO: Fold 3 Training C-index: 0.5343
2025-07-11 14:44:54,831 - INFO: Fold 5 Training C-index: 0.5172
2025-07-11 14:44:55,063 - INFO: Fold 5 Test C-index: 0.5463
2025-07-11 14:44:55,063 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:55,073 - INFO: Fold 5 Validation C-index: 0.5086
2025-07-11 14:44:55,073 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:55,073 - INFO: C-indices per fold: ['0.5111', '0.5083', '0.5072', '0.5135', '0.5086']
2025-07-11 14:44:55,073 - INFO: Mean C-index: 0.5097
2025-07-11 14:44:55,073 - INFO: Std Dev of C-index: 0.0023
2025-07-11 14:44:55,073 - INFO: Worst C-index: 0.5072
2025-07-11 14:44:55,075 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:55,075 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-11 14:44:55,084 - INFO: Fold 3 Test C-index: 0.5450
2025-07-11 14:44:55,084 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:55,096 - INFO: Fold 3 Validation C-index: 0.5099
2025-07-11 14:44:55,096 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:55,104 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:55,105 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:55,105 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:55,105 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,105 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,105 - INFO: Starting regression paradigm
2025-07-11 14:44:55,105 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:55,105 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,132 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:55,135 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:55,140 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:55,165 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:55,170 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:55,170 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:55,170 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,170 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,170 - INFO: Starting regression paradigm
2025-07-11 14:44:55,170 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:55,170 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,321 - INFO: Fold 4 Training C-index: 0.5498
2025-07-11 14:44:55,566 - INFO: Fold 4 Test C-index: 0.4872
2025-07-11 14:44:55,566 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:55,578 - INFO: Fold 4 Validation C-index: 0.5175
2025-07-11 14:44:55,578 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:55,587 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:55,588 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:55,588 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:55,588 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,588 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:55,588 - INFO: Starting regression paradigm
2025-07-11 14:44:55,588 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:55,588 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:55,807 - INFO: Fold 5 Training C-index: 0.5397
2025-07-11 14:44:55,945 - INFO: Fold 1 Training C-index: 0.5273
2025-07-11 14:44:56,138 - INFO: Fold 5 Test C-index: 0.5243
2025-07-11 14:44:56,139 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:56,181 - INFO: Fold 5 Validation C-index: 0.5082
2025-07-11 14:44:56,182 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:56,182 - INFO: C-indices per fold: ['0.5123', '0.5132', '0.5099', '0.5175', '0.5082']
2025-07-11 14:44:56,182 - INFO: Mean C-index: 0.5122
2025-07-11 14:44:56,182 - INFO: Std Dev of C-index: 0.0032
2025-07-11 14:44:56,183 - INFO: Worst C-index: 0.5082
2025-07-11 14:44:56,185 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 4 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:56,185 - INFO: --- Starting Round 4 with Seed 6 ---
2025-07-11 14:44:56,204 - INFO: Fold 1 Test C-index: 0.5247
2025-07-11 14:44:56,205 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:56,217 - INFO: Fold 1 Validation C-index: 0.4974
2025-07-11 14:44:56,217 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:56,224 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,224 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,224 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,224 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,225 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,225 - INFO: Starting regression paradigm
2025-07-11 14:44:56,225 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:56,225 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:56,261 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:56,264 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:56,269 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:56,297 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,301 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,301 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,301 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,302 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,302 - INFO: Starting regression paradigm
2025-07-11 14:44:56,302 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:56,302 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:56,415 - INFO: Fold 2 Training C-index: 0.5258
2025-07-11 14:44:56,612 - INFO: Fold 2 Test C-index: 0.5182
2025-07-11 14:44:56,612 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:56,623 - INFO: Fold 2 Validation C-index: 0.4908
2025-07-11 14:44:56,623 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:56,629 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:56,630 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:56,630 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:56,630 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,630 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:56,630 - INFO: Starting regression paradigm
2025-07-11 14:44:56,630 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:56,630 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:56,808 - INFO: Fold 3 Training C-index: 0.5217
2025-07-11 14:44:57,073 - INFO: Fold 3 Test C-index: 0.5282
2025-07-11 14:44:57,074 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:57,078 - INFO: Fold 1 Training C-index: 0.5273
2025-07-11 14:44:57,085 - INFO: Fold 3 Validation C-index: 0.4955
2025-07-11 14:44:57,085 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:57,091 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,092 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,092 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,092 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,092 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,092 - INFO: Starting regression paradigm
2025-07-11 14:44:57,092 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:57,092 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,285 - INFO: Fold 4 Training C-index: 0.5222
2025-07-11 14:44:57,303 - INFO: Fold 1 Test C-index: 0.5164
2025-07-11 14:44:57,303 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:57,315 - INFO: Fold 1 Validation C-index: 0.5206
2025-07-11 14:44:57,315 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:57,323 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,324 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,325 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,325 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,325 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,325 - INFO: Starting regression paradigm
2025-07-11 14:44:57,325 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:57,325 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,479 - INFO: Fold 4 Test C-index: 0.5343
2025-07-11 14:44:57,480 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:57,490 - INFO: Fold 4 Validation C-index: 0.4917
2025-07-11 14:44:57,490 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:57,496 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,497 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,497 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,497 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,497 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,497 - INFO: Starting regression paradigm
2025-07-11 14:44:57,497 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:57,497 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,556 - INFO: Fold 2 Training C-index: 0.5359
2025-07-11 14:44:57,688 - INFO: Fold 5 Training C-index: 0.5299
2025-07-11 14:44:57,782 - INFO: Fold 2 Test C-index: 0.5349
2025-07-11 14:44:57,782 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:57,793 - INFO: Fold 2 Validation C-index: 0.5074
2025-07-11 14:44:57,793 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:57,801 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:57,802 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:57,802 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:57,802 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,802 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:57,802 - INFO: Starting regression paradigm
2025-07-11 14:44:57,802 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:57,802 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:57,872 - INFO: Fold 5 Test C-index: 0.5055
2025-07-11 14:44:57,873 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:57,882 - INFO: Fold 5 Validation C-index: 0.4916
2025-07-11 14:44:57,882 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:57,883 - INFO: C-indices per fold: ['0.4974', '0.4908', '0.4955', '0.4917', '0.4916']
2025-07-11 14:44:57,883 - INFO: Mean C-index: 0.4934
2025-07-11 14:44:57,883 - INFO: Std Dev of C-index: 0.0026
2025-07-11 14:44:57,883 - INFO: Worst C-index: 0.4908
2025-07-11 14:44:57,884 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:57,884 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-11 14:44:57,960 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:57,965 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:57,973 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:58,053 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:58,059 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:58,059 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:58,059 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,059 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,059 - INFO: Starting regression paradigm
2025-07-11 14:44:58,059 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:58,059 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:58,080 - INFO: Fold 3 Training C-index: 0.5301
2025-07-11 14:44:58,302 - INFO: Fold 3 Test C-index: 0.5357
2025-07-11 14:44:58,302 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:58,315 - INFO: Fold 3 Validation C-index: 0.5183
2025-07-11 14:44:58,315 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:58,324 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:58,325 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:58,326 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:58,326 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,326 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,326 - INFO: Starting regression paradigm
2025-07-11 14:44:58,326 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:58,326 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:58,575 - INFO: Fold 4 Training C-index: 0.5342
2025-07-11 14:44:58,855 - INFO: Fold 4 Test C-index: 0.5297
2025-07-11 14:44:58,855 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:44:58,865 - INFO: Fold 1 Training C-index: 0.5391
2025-07-11 14:44:58,867 - INFO: Fold 4 Validation C-index: 0.5118
2025-07-11 14:44:58,868 - INFO: --- Fold 5/5 ---
2025-07-11 14:44:58,876 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:58,877 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:58,877 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:58,877 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,877 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:58,877 - INFO: Starting regression paradigm
2025-07-11 14:44:58,877 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:44:58,877 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,063 - INFO: Fold 1 Test C-index: 0.5292
2025-07-11 14:44:59,063 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:44:59,073 - INFO: Fold 1 Validation C-index: 0.4927
2025-07-11 14:44:59,073 - INFO: --- Fold 2/5 ---
2025-07-11 14:44:59,080 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,080 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,080 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,081 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,081 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,081 - INFO: Starting regression paradigm
2025-07-11 14:44:59,081 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:44:59,081 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,107 - INFO: Fold 5 Training C-index: 0.5394
2025-07-11 14:44:59,302 - INFO: Fold 2 Training C-index: 0.5239
2025-07-11 14:44:59,408 - INFO: Fold 5 Test C-index: 0.5037
2025-07-11 14:44:59,408 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:44:59,422 - INFO: Fold 5 Validation C-index: 0.5118
2025-07-11 14:44:59,422 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:44:59,422 - INFO: C-indices per fold: ['0.5206', '0.5074', '0.5183', '0.5118', '0.5118']
2025-07-11 14:44:59,422 - INFO: Mean C-index: 0.5140
2025-07-11 14:44:59,422 - INFO: Std Dev of C-index: 0.0048
2025-07-11 14:44:59,423 - INFO: Worst C-index: 0.5074
2025-07-11 14:44:59,424 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 5 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:44:59,424 - INFO: --- Starting Round 5 with Seed 7 ---
2025-07-11 14:44:59,489 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:44:59,492 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:44:59,498 - INFO: --- Fold 1/5 ---
2025-07-11 14:44:59,529 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,534 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,534 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,535 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,535 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,535 - INFO: Starting regression paradigm
2025-07-11 14:44:59,535 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:44:59,535 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,544 - INFO: Fold 2 Test C-index: 0.5227
2025-07-11 14:44:59,544 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:44:59,555 - INFO: Fold 2 Validation C-index: 0.4938
2025-07-11 14:44:59,556 - INFO: --- Fold 3/5 ---
2025-07-11 14:44:59,562 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,562 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,563 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,563 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,563 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,563 - INFO: Starting regression paradigm
2025-07-11 14:44:59,563 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:44:59,563 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:44:59,756 - INFO: Fold 3 Training C-index: 0.5278
2025-07-11 14:44:59,951 - INFO: Fold 3 Test C-index: 0.5092
2025-07-11 14:44:59,951 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:44:59,962 - INFO: Fold 3 Validation C-index: 0.4909
2025-07-11 14:44:59,962 - INFO: --- Fold 4/5 ---
2025-07-11 14:44:59,969 - INFO: Features for this fold have been standardized.
2025-07-11 14:44:59,969 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:44:59,970 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:44:59,970 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,970 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:44:59,970 - INFO: Starting regression paradigm
2025-07-11 14:44:59,970 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:44:59,970 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:00,182 - INFO: Fold 4 Training C-index: 0.5245
2025-07-11 14:45:00,595 - INFO: Fold 4 Test C-index: 0.5384
2025-07-11 14:45:00,595 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:00,619 - INFO: Fold 4 Validation C-index: 0.4964
2025-07-11 14:45:00,619 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:00,632 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:00,634 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:00,634 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:00,634 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:00,635 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:00,635 - INFO: Starting regression paradigm
2025-07-11 14:45:00,635 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:00,635 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:00,880 - INFO: Fold 1 Training C-index: 0.5391
2025-07-11 14:45:01,041 - INFO: Fold 5 Training C-index: 0.5350
2025-07-11 14:45:01,276 - INFO: Fold 1 Test C-index: 0.5161
2025-07-11 14:45:01,276 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:01,299 - INFO: Fold 1 Validation C-index: 0.5210
2025-07-11 14:45:01,299 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:01,313 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:01,315 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:01,316 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:01,316 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,316 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,316 - INFO: Starting regression paradigm
2025-07-11 14:45:01,316 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:01,317 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:01,355 - INFO: Fold 5 Test C-index: 0.4987
2025-07-11 14:45:01,355 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:01,373 - INFO: Fold 5 Validation C-index: 0.4886
2025-07-11 14:45:01,374 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:01,374 - INFO: C-indices per fold: ['0.4927', '0.4938', '0.4909', '0.4964', '0.4886']
2025-07-11 14:45:01,374 - INFO: Mean C-index: 0.4925
2025-07-11 14:45:01,374 - INFO: Std Dev of C-index: 0.0026
2025-07-11 14:45:01,374 - INFO: Worst C-index: 0.4886
2025-07-11 14:45:01,375 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:01,376 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-11 14:45:01,466 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:01,470 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:01,477 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:01,514 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:01,521 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:01,522 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:01,522 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,522 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:01,522 - INFO: Starting regression paradigm
2025-07-11 14:45:01,522 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:01,522 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:01,635 - INFO: Fold 2 Training C-index: 0.5297
2025-07-11 14:45:01,987 - INFO: Fold 2 Test C-index: 0.5192
2025-07-11 14:45:01,988 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:02,000 - INFO: Fold 2 Validation C-index: 0.5104
2025-07-11 14:45:02,000 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:02,008 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:02,009 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:02,009 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:02,010 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,010 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,010 - INFO: Starting regression paradigm
2025-07-11 14:45:02,010 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:02,010 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:02,238 - INFO: Fold 3 Training C-index: 0.5362
2025-07-11 14:45:02,471 - INFO: Fold 1 Training C-index: 0.5363
2025-07-11 14:45:02,531 - INFO: Fold 3 Test C-index: 0.5240
2025-07-11 14:45:02,531 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:02,544 - INFO: Fold 3 Validation C-index: 0.5022
2025-07-11 14:45:02,544 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:02,554 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:02,555 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:02,555 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:02,555 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,555 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,555 - INFO: Starting regression paradigm
2025-07-11 14:45:02,555 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:02,556 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:02,668 - INFO: Fold 1 Test C-index: 0.4993
2025-07-11 14:45:02,668 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:02,679 - INFO: Fold 1 Validation C-index: 0.5017
2025-07-11 14:45:02,679 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:02,686 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:02,687 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:02,687 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:02,687 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,687 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:02,687 - INFO: Starting regression paradigm
2025-07-11 14:45:02,687 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:02,687 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:02,791 - INFO: Fold 4 Training C-index: 0.5293
2025-07-11 14:45:03,005 - INFO: Fold 2 Training C-index: 0.5020
2025-07-11 14:45:03,042 - INFO: Fold 4 Test C-index: 0.5487
2025-07-11 14:45:03,042 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:03,057 - INFO: Fold 4 Validation C-index: 0.5180
2025-07-11 14:45:03,057 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:03,067 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,068 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,068 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,069 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,069 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,069 - INFO: Starting regression paradigm
2025-07-11 14:45:03,069 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:03,069 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,238 - INFO: Fold 2 Test C-index: 0.5137
2025-07-11 14:45:03,239 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:03,270 - INFO: Fold 2 Validation C-index: 0.5009
2025-07-11 14:45:03,271 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:03,293 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,294 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,294 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,294 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,294 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,294 - INFO: Starting regression paradigm
2025-07-11 14:45:03,294 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:03,295 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,374 - INFO: Fold 5 Training C-index: 0.5442
2025-07-11 14:45:03,488 - INFO: Fold 3 Training C-index: 0.5064
2025-07-11 14:45:03,604 - INFO: Fold 5 Test C-index: 0.4962
2025-07-11 14:45:03,604 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:03,616 - INFO: Fold 5 Validation C-index: 0.5125
2025-07-11 14:45:03,616 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:03,616 - INFO: C-indices per fold: ['0.5210', '0.5104', '0.5022', '0.5180', '0.5125']
2025-07-11 14:45:03,617 - INFO: Mean C-index: 0.5128
2025-07-11 14:45:03,617 - INFO: Std Dev of C-index: 0.0065
2025-07-11 14:45:03,617 - INFO: Worst C-index: 0.5022
2025-07-11 14:45:03,618 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 6 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:03,618 - INFO: --- Starting Round 6 with Seed 8 ---
2025-07-11 14:45:03,685 - INFO: Fold 3 Test C-index: 0.4913
2025-07-11 14:45:03,685 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:03,686 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:03,689 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:03,694 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:03,695 - INFO: Fold 3 Validation C-index: 0.5015
2025-07-11 14:45:03,695 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:03,701 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,702 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,702 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,702 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,703 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,703 - INFO: Starting regression paradigm
2025-07-11 14:45:03,703 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:03,703 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,723 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:03,726 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:03,727 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:03,727 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,727 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:03,727 - INFO: Starting regression paradigm
2025-07-11 14:45:03,727 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:03,727 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:03,904 - INFO: Fold 4 Training C-index: 0.5046
2025-07-11 14:45:04,094 - INFO: Fold 4 Test C-index: 0.4823
2025-07-11 14:45:04,094 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:04,106 - INFO: Fold 4 Validation C-index: 0.5021
2025-07-11 14:45:04,106 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:04,112 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:04,113 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:04,113 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:04,113 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,113 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,113 - INFO: Starting regression paradigm
2025-07-11 14:45:04,113 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:04,113 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:04,298 - INFO: Fold 5 Training C-index: 0.4980
2025-07-11 14:45:04,516 - INFO: Fold 1 Training C-index: 0.5363
2025-07-11 14:45:04,567 - INFO: Fold 5 Test C-index: 0.5203
2025-07-11 14:45:04,567 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:04,579 - INFO: Fold 5 Validation C-index: 0.4995
2025-07-11 14:45:04,579 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:04,579 - INFO: C-indices per fold: ['0.5017', '0.5009', '0.5015', '0.5021', '0.4995']
2025-07-11 14:45:04,579 - INFO: Mean C-index: 0.5011
2025-07-11 14:45:04,580 - INFO: Std Dev of C-index: 0.0009
2025-07-11 14:45:04,580 - INFO: Worst C-index: 0.4995
2025-07-11 14:45:04,581 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:04,581 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-11 14:45:04,657 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:04,661 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:04,666 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:04,693 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:04,697 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:04,697 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:04,698 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,698 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,698 - INFO: Starting regression paradigm
2025-07-11 14:45:04,698 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:04,698 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:04,841 - INFO: Fold 1 Test C-index: 0.5135
2025-07-11 14:45:04,842 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:04,852 - INFO: Fold 1 Validation C-index: 0.5197
2025-07-11 14:45:04,852 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:04,860 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:04,861 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:04,861 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:04,861 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,861 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:04,861 - INFO: Starting regression paradigm
2025-07-11 14:45:04,861 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:04,861 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,168 - INFO: Fold 2 Training C-index: 0.5291
2025-07-11 14:45:05,382 - INFO: Fold 2 Test C-index: 0.5355
2025-07-11 14:45:05,382 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:05,394 - INFO: Fold 2 Validation C-index: 0.5135
2025-07-11 14:45:05,394 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:05,402 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,403 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,403 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,403 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,403 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,403 - INFO: Starting regression paradigm
2025-07-11 14:45:05,403 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:05,403 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,553 - INFO: Fold 1 Training C-index: 0.5346
2025-07-11 14:45:05,697 - INFO: Fold 3 Training C-index: 0.5330
2025-07-11 14:45:05,743 - INFO: Fold 1 Test C-index: 0.5304
2025-07-11 14:45:05,743 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:05,752 - INFO: Fold 1 Validation C-index: 0.4977
2025-07-11 14:45:05,753 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:05,758 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,759 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,759 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,759 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,759 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,759 - INFO: Starting regression paradigm
2025-07-11 14:45:05,759 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:05,759 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,912 - INFO: Fold 3 Test C-index: 0.5330
2025-07-11 14:45:05,912 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:05,924 - INFO: Fold 3 Validation C-index: 0.5213
2025-07-11 14:45:05,924 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:05,932 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:05,933 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:05,933 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:05,933 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,933 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:05,933 - INFO: Starting regression paradigm
2025-07-11 14:45:05,933 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:05,933 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:05,936 - INFO: Fold 2 Training C-index: 0.5120
2025-07-11 14:45:06,112 - INFO: Fold 2 Test C-index: 0.5124
2025-07-11 14:45:06,112 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:06,121 - INFO: Fold 2 Validation C-index: 0.4986
2025-07-11 14:45:06,121 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:06,127 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,128 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,128 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,128 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,128 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,128 - INFO: Starting regression paradigm
2025-07-11 14:45:06,128 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:06,128 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,154 - INFO: Fold 4 Training C-index: 0.5440
2025-07-11 14:45:06,347 - INFO: Fold 3 Training C-index: 0.5138
2025-07-11 14:45:06,443 - INFO: Fold 4 Test C-index: 0.5109
2025-07-11 14:45:06,444 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:06,455 - INFO: Fold 4 Validation C-index: 0.5128
2025-07-11 14:45:06,455 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:06,462 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,463 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,463 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,464 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,464 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,464 - INFO: Starting regression paradigm
2025-07-11 14:45:06,464 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:06,464 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,572 - INFO: Fold 3 Test C-index: 0.5074
2025-07-11 14:45:06,572 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:06,583 - INFO: Fold 3 Validation C-index: 0.4997
2025-07-11 14:45:06,583 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:06,590 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,591 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,591 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,592 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,592 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,592 - INFO: Starting regression paradigm
2025-07-11 14:45:06,592 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:06,592 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,694 - INFO: Fold 5 Training C-index: 0.5358
2025-07-11 14:45:06,778 - INFO: Fold 4 Training C-index: 0.5159
2025-07-11 14:45:06,900 - INFO: Fold 5 Test C-index: 0.5258
2025-07-11 14:45:06,900 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:06,911 - INFO: Fold 5 Validation C-index: 0.5163
2025-07-11 14:45:06,911 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:06,911 - INFO: C-indices per fold: ['0.5197', '0.5135', '0.5213', '0.5128', '0.5163']
2025-07-11 14:45:06,911 - INFO: Mean C-index: 0.5167
2025-07-11 14:45:06,911 - INFO: Std Dev of C-index: 0.0033
2025-07-11 14:45:06,911 - INFO: Worst C-index: 0.5128
2025-07-11 14:45:06,912 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 7 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:06,912 - INFO: --- Starting Round 7 with Seed 9 ---
2025-07-11 14:45:06,961 - INFO: Fold 4 Test C-index: 0.4925
2025-07-11 14:45:06,961 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:06,970 - INFO: Fold 4 Validation C-index: 0.4989
2025-07-11 14:45:06,971 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:06,977 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:06,977 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:06,978 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:06,978 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,978 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:06,978 - INFO: Starting regression paradigm
2025-07-11 14:45:06,978 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:06,978 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:06,983 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:06,986 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:06,991 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:07,020 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:07,025 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:07,026 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:07,026 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,026 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,026 - INFO: Starting regression paradigm
2025-07-11 14:45:07,026 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:07,026 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:07,179 - INFO: Fold 5 Training C-index: 0.5163
2025-07-11 14:45:07,379 - INFO: Fold 5 Test C-index: 0.5034
2025-07-11 14:45:07,379 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:07,390 - INFO: Fold 5 Validation C-index: 0.4974
2025-07-11 14:45:07,390 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:07,390 - INFO: C-indices per fold: ['0.4977', '0.4986', '0.4997', '0.4989', '0.4974']
2025-07-11 14:45:07,390 - INFO: Mean C-index: 0.4985
2025-07-11 14:45:07,391 - INFO: Std Dev of C-index: 0.0008
2025-07-11 14:45:07,391 - INFO: Worst C-index: 0.4974
2025-07-11 14:45:07,391 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:07,392 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-11 14:45:07,465 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:07,469 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:07,475 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:07,507 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:07,512 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:07,512 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:07,512 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,512 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:07,512 - INFO: Starting regression paradigm
2025-07-11 14:45:07,512 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:07,512 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:07,836 - INFO: Fold 1 Training C-index: 0.5346
2025-07-11 14:45:08,195 - INFO: Fold 1 Test C-index: 0.5401
2025-07-11 14:45:08,195 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:08,214 - INFO: Fold 1 Validation C-index: 0.5199
2025-07-11 14:45:08,215 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:08,228 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,230 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,230 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,230 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,230 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,230 - INFO: Starting regression paradigm
2025-07-11 14:45:08,230 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:08,230 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:08,473 - INFO: Fold 2 Training C-index: 0.5361
2025-07-11 14:45:08,606 - INFO: Fold 1 Training C-index: 0.5410
2025-07-11 14:45:08,771 - INFO: Fold 2 Test C-index: 0.5183
2025-07-11 14:45:08,772 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:08,783 - INFO: Fold 2 Validation C-index: 0.5206
2025-07-11 14:45:08,783 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:08,792 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,793 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,793 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,793 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,793 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,793 - INFO: Starting regression paradigm
2025-07-11 14:45:08,793 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:08,793 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:08,798 - INFO: Fold 1 Test C-index: 0.5090
2025-07-11 14:45:08,798 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:08,810 - INFO: Fold 1 Validation C-index: 0.5171
2025-07-11 14:45:08,810 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:08,817 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:08,818 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:08,818 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:08,818 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,818 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:08,818 - INFO: Starting regression paradigm
2025-07-11 14:45:08,818 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:08,818 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:08,997 - INFO: Fold 2 Training C-index: 0.5117
2025-07-11 14:45:09,017 - INFO: Fold 3 Training C-index: 0.5279
2025-07-11 14:45:09,192 - INFO: Fold 2 Test C-index: 0.5329
2025-07-11 14:45:09,193 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:09,204 - INFO: Fold 2 Validation C-index: 0.5170
2025-07-11 14:45:09,204 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:09,210 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:09,211 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:09,211 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:09,211 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,211 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,212 - INFO: Starting regression paradigm
2025-07-11 14:45:09,212 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:09,212 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:09,259 - INFO: Fold 3 Test C-index: 0.5392
2025-07-11 14:45:09,259 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:09,272 - INFO: Fold 3 Validation C-index: 0.5244
2025-07-11 14:45:09,272 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:09,281 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:09,282 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:09,282 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:09,282 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,282 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,283 - INFO: Starting regression paradigm
2025-07-11 14:45:09,283 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:09,283 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:09,418 - INFO: Fold 3 Training C-index: 0.5128
2025-07-11 14:45:09,519 - INFO: Fold 4 Training C-index: 0.5370
2025-07-11 14:45:09,640 - INFO: Fold 3 Test C-index: 0.5171
2025-07-11 14:45:09,640 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:09,657 - INFO: Fold 3 Validation C-index: 0.5175
2025-07-11 14:45:09,657 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:09,686 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:09,687 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:09,688 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:09,688 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,689 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,689 - INFO: Starting regression paradigm
2025-07-11 14:45:09,693 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:09,693 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:09,846 - INFO: Fold 4 Test C-index: 0.5055
2025-07-11 14:45:09,846 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:09,862 - INFO: Fold 4 Validation C-index: 0.5210
2025-07-11 14:45:09,863 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:09,873 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:09,874 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:09,875 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:09,875 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,875 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:09,875 - INFO: Starting regression paradigm
2025-07-11 14:45:09,875 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:09,875 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:09,961 - INFO: Fold 4 Training C-index: 0.5187
2025-07-11 14:45:10,158 - INFO: Fold 5 Training C-index: 0.5316
2025-07-11 14:45:10,188 - INFO: Fold 4 Test C-index: 0.5018
2025-07-11 14:45:10,189 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:10,198 - INFO: Fold 4 Validation C-index: 0.5162
2025-07-11 14:45:10,198 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:10,205 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,205 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,205 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,206 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,206 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,206 - INFO: Starting regression paradigm
2025-07-11 14:45:10,206 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:10,206 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:10,378 - INFO: Fold 5 Test C-index: 0.5096
2025-07-11 14:45:10,378 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:10,391 - INFO: Fold 5 Validation C-index: 0.5087
2025-07-11 14:45:10,391 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:10,392 - INFO: C-indices per fold: ['0.5199', '0.5206', '0.5244', '0.5210', '0.5087']
2025-07-11 14:45:10,392 - INFO: Mean C-index: 0.5189
2025-07-11 14:45:10,392 - INFO: Std Dev of C-index: 0.0053
2025-07-11 14:45:10,392 - INFO: Worst C-index: 0.5087
2025-07-11 14:45:10,393 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 8 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:10,393 - INFO: --- Starting Round 8 with Seed 10 ---
2025-07-11 14:45:10,404 - INFO: Fold 5 Training C-index: 0.5148
2025-07-11 14:45:10,459 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:10,462 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:10,467 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:10,493 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,497 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,497 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,497 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,498 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,498 - INFO: Starting regression paradigm
2025-07-11 14:45:10,498 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:10,498 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:10,602 - INFO: Fold 5 Test C-index: 0.5146
2025-07-11 14:45:10,603 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:10,612 - INFO: Fold 5 Validation C-index: 0.5175
2025-07-11 14:45:10,612 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:10,612 - INFO: C-indices per fold: ['0.5171', '0.5170', '0.5175', '0.5162', '0.5175']
2025-07-11 14:45:10,612 - INFO: Mean C-index: 0.5171
2025-07-11 14:45:10,613 - INFO: Std Dev of C-index: 0.0005
2025-07-11 14:45:10,613 - INFO: Worst C-index: 0.5162
2025-07-11 14:45:10,614 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:10,614 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-11 14:45:10,676 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:10,680 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:10,685 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:10,715 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:10,721 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:10,721 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:10,722 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,722 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:10,722 - INFO: Starting regression paradigm
2025-07-11 14:45:10,722 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:10,722 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:11,381 - INFO: Fold 1 Training C-index: 0.5410
2025-07-11 14:45:11,724 - INFO: Fold 1 Test C-index: 0.5270
2025-07-11 14:45:11,724 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:11,737 - INFO: Fold 1 Training C-index: 0.5256
2025-07-11 14:45:11,737 - INFO: Fold 1 Validation C-index: 0.5282
2025-07-11 14:45:11,738 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:11,746 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:11,747 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:11,747 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:11,748 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,748 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,748 - INFO: Starting regression paradigm
2025-07-11 14:45:11,748 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:11,748 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:11,956 - INFO: Fold 1 Test C-index: 0.5258
2025-07-11 14:45:11,956 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:11,967 - INFO: Fold 1 Validation C-index: 0.5198
2025-07-11 14:45:11,967 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:11,973 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:11,974 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:11,974 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:11,974 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,975 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:11,975 - INFO: Starting regression paradigm
2025-07-11 14:45:11,975 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:11,975 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,004 - INFO: Fold 2 Training C-index: 0.5357
2025-07-11 14:45:12,180 - INFO: Fold 2 Training C-index: 0.5180
2025-07-11 14:45:12,242 - INFO: Fold 2 Test C-index: 0.5254
2025-07-11 14:45:12,242 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:12,255 - INFO: Fold 2 Validation C-index: 0.5339
2025-07-11 14:45:12,255 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:12,264 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,265 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,266 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,266 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,266 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,266 - INFO: Starting regression paradigm
2025-07-11 14:45:12,266 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:12,266 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,391 - INFO: Fold 2 Test C-index: 0.5013
2025-07-11 14:45:12,391 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:12,401 - INFO: Fold 2 Validation C-index: 0.5196
2025-07-11 14:45:12,401 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:12,407 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,407 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,407 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,408 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,408 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,408 - INFO: Starting regression paradigm
2025-07-11 14:45:12,408 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:12,408 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,505 - INFO: Fold 3 Training C-index: 0.5309
2025-07-11 14:45:12,602 - INFO: Fold 3 Training C-index: 0.5167
2025-07-11 14:45:12,788 - INFO: Fold 3 Test C-index: 0.5274
2025-07-11 14:45:12,789 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:12,808 - INFO: Fold 3 Validation C-index: 0.5369
2025-07-11 14:45:12,809 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:12,820 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,821 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,822 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,822 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,822 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,822 - INFO: Starting regression paradigm
2025-07-11 14:45:12,822 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:12,822 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:12,890 - INFO: Fold 3 Test C-index: 0.5089
2025-07-11 14:45:12,891 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:12,906 - INFO: Fold 3 Validation C-index: 0.5184
2025-07-11 14:45:12,906 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:12,913 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:12,913 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:12,914 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:12,914 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,914 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:12,914 - INFO: Starting regression paradigm
2025-07-11 14:45:12,914 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:12,914 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:13,189 - INFO: Fold 4 Training C-index: 0.5314
2025-07-11 14:45:13,245 - INFO: Fold 4 Training C-index: 0.5152
2025-07-11 14:45:13,434 - INFO: Fold 4 Test C-index: 0.5158
2025-07-11 14:45:13,434 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:13,445 - INFO: Fold 4 Test C-index: 0.5158
2025-07-11 14:45:13,445 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:13,445 - INFO: Fold 4 Validation C-index: 0.5349
2025-07-11 14:45:13,446 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:13,454 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:13,455 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:13,455 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:13,455 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,455 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,455 - INFO: Starting regression paradigm
2025-07-11 14:45:13,455 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:13,455 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:13,456 - INFO: Fold 4 Validation C-index: 0.5189
2025-07-11 14:45:13,456 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:13,462 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:13,462 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:13,462 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:13,462 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,463 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,463 - INFO: Starting regression paradigm
2025-07-11 14:45:13,463 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:13,463 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:13,645 - INFO: Fold 5 Training C-index: 0.5152
2025-07-11 14:45:13,671 - INFO: Fold 5 Training C-index: 0.5303
2025-07-11 14:45:13,827 - INFO: Fold 5 Test C-index: 0.5163
2025-07-11 14:45:13,827 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:13,837 - INFO: Fold 5 Validation C-index: 0.5164
2025-07-11 14:45:13,837 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:13,837 - INFO: C-indices per fold: ['0.5198', '0.5196', '0.5184', '0.5189', '0.5164']
2025-07-11 14:45:13,838 - INFO: Mean C-index: 0.5186
2025-07-11 14:45:13,838 - INFO: Std Dev of C-index: 0.0012
2025-07-11 14:45:13,838 - INFO: Worst C-index: 0.5164
2025-07-11 14:45:13,839 - INFO: ==================================================
2025-07-11 14:45:13,839 - INFO: Final Summary Over 10 Independent Run(s), validation set (hold-out set)
2025-07-11 14:45:13,839 - INFO: ==================================================
2025-07-11 14:45:13,839 - INFO: Average of 'mean_c_index' over 10 runs: 0.5050 (Std Dev: 0.0096)
2025-07-11 14:45:13,839 - INFO: Average of 'std_c_index' over 10 runs: 0.0018 (Std Dev: 0.0010)
2025-07-11 14:45:13,839 - INFO: Average of 'worst_c_index' over 10 runs: 0.5026 (Std Dev: 0.0098)
2025-07-11 14:45:13,839 - INFO: ==================================================
2025-07-11 14:45:13,839 - INFO: Final Summary Over 10 Independent Run(s), test set (in cross-validation)
2025-07-11 14:45:13,839 - INFO: ==================================================
2025-07-11 14:45:13,839 - INFO: Average of 'mean_c_index_test' over 10 runs: 0.5150 (Std Dev: 0.0057)
2025-07-11 14:45:13,840 - INFO: Average of 'std_c_index_test' over 10 runs: 0.0169 (Std Dev: 0.0076)
2025-07-11 14:45:13,840 - INFO: Average of 'worst_c_index_test' over 10 runs: 0.4903 (Std Dev: 0.0132)
2025-07-11 14:45:13,883 - INFO: Fold 5 Test C-index: 0.5213
2025-07-11 14:45:13,883 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:13,893 - INFO: Fold 5 Validation C-index: 0.5318
2025-07-11 14:45:13,894 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:13,894 - INFO: C-indices per fold: ['0.5282', '0.5339', '0.5369', '0.5349', '0.5318']
2025-07-11 14:45:13,894 - INFO: Mean C-index: 0.5332
2025-07-11 14:45:13,894 - INFO: Std Dev of C-index: 0.0030
2025-07-11 14:45:13,894 - INFO: Worst C-index: 0.5282
2025-07-11 14:45:13,895 - INFO: 
 
 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*= Round 9 =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
2025-07-11 14:45:13,895 - INFO: --- Starting Round 9 with Seed 11 ---
2025-07-11 14:45:13,952 - INFO: Successfully loaded data from processed_equipment_data_all.csv. Shape: (1975, 187)
2025-07-11 14:45:13,955 - INFO: Succsessfully get feature columns. Total: 180
2025-07-11 14:45:13,960 - INFO: --- Fold 1/5 ---
2025-07-11 14:45:13,990 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:13,994 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:13,994 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:13,994 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,994 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:13,994 - INFO: Starting regression paradigm
2025-07-11 14:45:13,994 - INFO: Training backend model 'LogLogistic' on fold 1...
2025-07-11 14:45:13,994 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:14,726 - INFO: Fold 1 Training C-index: 0.5256
2025-07-11 14:45:14,904 - INFO: Fold 1 Test C-index: 0.5117
2025-07-11 14:45:14,904 - INFO: Evaluating model on validation set of fold 1...
2025-07-11 14:45:14,912 - INFO: Fold 1 Validation C-index: 0.5412
2025-07-11 14:45:14,912 - INFO: --- Fold 2/5 ---
2025-07-11 14:45:14,919 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:14,919 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:14,919 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:14,920 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:14,920 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:14,920 - INFO: Starting regression paradigm
2025-07-11 14:45:14,920 - INFO: Training backend model 'LogLogistic' on fold 2...
2025-07-11 14:45:14,920 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:15,096 - INFO: Fold 2 Training C-index: 0.5234
2025-07-11 14:45:15,299 - INFO: Fold 2 Test C-index: 0.5073
2025-07-11 14:45:15,299 - INFO: Evaluating model on validation set of fold 2...
2025-07-11 14:45:15,311 - INFO: Fold 2 Validation C-index: 0.5497
2025-07-11 14:45:15,311 - INFO: --- Fold 3/5 ---
2025-07-11 14:45:15,321 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:15,322 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:15,323 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:15,323 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:15,323 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:15,323 - INFO: Starting regression paradigm
2025-07-11 14:45:15,323 - INFO: Training backend model 'LogLogistic' on fold 3...
2025-07-11 14:45:15,323 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:15,598 - INFO: Fold 3 Training C-index: 0.5226
2025-07-11 14:45:15,890 - INFO: Fold 3 Test C-index: 0.5314
2025-07-11 14:45:15,890 - INFO: Evaluating model on validation set of fold 3...
2025-07-11 14:45:15,907 - INFO: Fold 3 Validation C-index: 0.5429
2025-07-11 14:45:15,907 - INFO: --- Fold 4/5 ---
2025-07-11 14:45:15,917 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:15,918 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:15,918 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:15,919 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:15,919 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:15,919 - INFO: Starting regression paradigm
2025-07-11 14:45:15,919 - INFO: Training backend model 'LogLogistic' on fold 4...
2025-07-11 14:45:15,919 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:16,275 - INFO: Fold 4 Training C-index: 0.5253
2025-07-11 14:45:16,700 - INFO: Fold 4 Test C-index: 0.5087
2025-07-11 14:45:16,701 - INFO: Evaluating model on validation set of fold 4...
2025-07-11 14:45:16,718 - INFO: Fold 4 Validation C-index: 0.5381
2025-07-11 14:45:16,718 - INFO: --- Fold 5/5 ---
2025-07-11 14:45:16,730 - INFO: Features for this fold have been standardized.
2025-07-11 14:45:16,731 - INFO: Weight statistics - mean: 1.000000, min: 1.000000, max: 1.000000
2025-07-11 14:45:16,731 - INFO: After normalization - max: 1.000000, min: 1.000000
2025-07-11 14:45:16,732 - INFO: W before clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:16,732 - INFO: W after clip: min=1.000000, max=1.000000, mean=1.000000, std=0.000000
2025-07-11 14:45:16,732 - INFO: Starting regression paradigm
2025-07-11 14:45:16,732 - INFO: Training backend model 'LogLogistic' on fold 5...
2025-07-11 14:45:16,732 - INFO: Using penalizer 0.03 for Weighted_cox
2025-07-11 14:45:16,991 - INFO: Fold 5 Training C-index: 0.5268
2025-07-11 14:45:17,193 - INFO: Fold 5 Test C-index: 0.5058
2025-07-11 14:45:17,193 - INFO: Evaluating model on validation set of fold 5...
2025-07-11 14:45:17,201 - INFO: Fold 5 Validation C-index: 0.5409
2025-07-11 14:45:17,202 - INFO: --- Cross-Validation Summary for this Round ---
2025-07-11 14:45:17,202 - INFO: C-indices per fold: ['0.5412', '0.5497', '0.5429', '0.5381', '0.5409']
2025-07-11 14:45:17,202 - INFO: Mean C-index: 0.5426
2025-07-11 14:45:17,202 - INFO: Std Dev of C-index: 0.0039
2025-07-11 14:45:17,202 - INFO: Worst C-index: 0.5381
2025-07-11 14:45:17,203 - INFO: ==================================================
2025-07-11 14:45:17,203 - INFO: Final Summary Over 10 Independent Run(s), validation set (hold-out set)
2025-07-11 14:45:17,203 - INFO: ==================================================
2025-07-11 14:45:17,203 - INFO: Average of 'mean_c_index' over 10 runs: 0.5161 (Std Dev: 0.0134)
2025-07-11 14:45:17,203 - INFO: Average of 'std_c_index' over 10 runs: 0.0039 (Std Dev: 0.0013)
2025-07-11 14:45:17,203 - INFO: Average of 'worst_c_index' over 10 runs: 0.5106 (Std Dev: 0.0132)
2025-07-11 14:45:17,203 - INFO: ==================================================
2025-07-11 14:45:17,203 - INFO: Final Summary Over 10 Independent Run(s), test set (in cross-validation)
2025-07-11 14:45:17,203 - INFO: ==================================================
2025-07-11 14:45:17,203 - INFO: Average of 'mean_c_index_test' over 10 runs: 0.5237 (Std Dev: 0.0045)
2025-07-11 14:45:17,203 - INFO: Average of 'std_c_index_test' over 10 runs: 0.0138 (Std Dev: 0.0055)
2025-07-11 14:45:17,204 - INFO: Average of 'worst_c_index_test' over 10 runs: 0.5026 (Std Dev: 0.0097)
