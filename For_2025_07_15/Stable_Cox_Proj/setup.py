"""
Stable Cox Equipment Model 安装脚本
"""

import os
import sys
import subprocess

def install_requirements():
    """安装依赖库"""
    print("安装依赖库...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ 依赖库安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖库安装失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("创建目录结构...")
    
    directories = [
        "models",
        "results", 
        "plots",
        "logs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ 创建目录: {directory}")
        else:
            print(f"✓ 目录已存在: {directory}")

def run_tests():
    """运行测试"""
    print("运行安装测试...")
    
    try:
        result = subprocess.run([sys.executable, "test_installation.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 安装测试通过")
            return True
        else:
            print("✗ 安装测试失败")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 测试运行失败: {e}")
        return False

def main():
    """主安装函数"""
    print("Stable Cox Equipment Model 安装程序")
    print("=" * 50)
    
    # 检查 Python 版本
    if sys.version_info < (3, 7):
        print("✗ 需要 Python 3.7 或更高版本")
        return False
    
    print(f"✓ Python 版本: {sys.version}")
    
    # 安装依赖
    if not install_requirements():
        print("安装失败：无法安装依赖库")
        return False
    
    # 创建目录
    create_directories()
    
    # 运行测试
    if not run_tests():
        print("安装失败：测试未通过")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Stable Cox Equipment Model 安装成功！")
    print("\n快速开始：")
    print("1. python example_usage.py  # 运行示例")
    print("2. 查看 README.md 了解详细用法")
    print("3. 使用您的数据开始分析")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
